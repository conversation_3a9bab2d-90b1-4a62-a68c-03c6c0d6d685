# Nimbus Makefile

.PHONY: build test clean install docker help dev

# 变量定义
PROJECT_NAME := nimbus
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "v1.0.0")
BUILD_TIME := $(shell date '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS := -X 'github.com/kingsoft/nimbus/pkg/version.Version=$(VERSION)' \
           -X 'github.com/kingsoft/nimbus/pkg/version.BuildTime=$(BUILD_TIME)' \
           -X 'github.com/kingsoft/nimbus/pkg/version.GitCommit=$(GIT_COMMIT)'

# 默认目标
all: clean build

# 构建项目
build:
	@echo "🔨 构建 $(PROJECT_NAME) $(VERSION)..."
	@mkdir -p bin
	@go build -ldflags "$(LDFLAGS)" -o bin/nimbus cmd/nimbus/main.go
	@go build -ldflags "$(LDFLAGS)" -o bin/kce-ctl cmd/kce-ctl/main.go
	@chmod +x bin/*
	@echo "✅ 构建完成!"

# 运行测试
test:
	@echo "🧪 运行测试..."
	@go test -v ./...

# 运行基准测试
benchmark:
	@echo "📈 运行基准测试..."
	@go test -bench=. -benchmem ./...

# 代码质量检查
lint:
	@echo "🔍 运行代码检查..."
	@golangci-lint run

# 格式化代码
fmt:
	@echo "🎨 格式化代码..."
	@go fmt ./...

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf bin/
	@rm -rf logs/
	@go clean

# 安装依赖
deps:
	@echo "📦 安装依赖..."
	@go mod tidy
	@go mod download

# 构建 Docker 镜像
docker:
	@echo "🐳 构建 Docker 镜像..."
	@docker build -t nimbus:$(VERSION) -f deploy/docker/Dockerfile .
	@docker tag nimbus:$(VERSION) nimbus:latest

# 开发模式
dev: build
	@echo "🚀 启动开发模式..."
	@./bin/nimbus

# 生成 Swagger 文档
swagger:
	@echo "📚 生成 Swagger 文档..."
	@swag init -g cmd/nimbus/main.go

# 运行数据库迁移
migrate:
	@echo "🗄️ 运行数据库迁移..."
	@./bin/nimbus migrate

# 启动所有服务（用于开发）
services:
	@echo "🚀 启动开发环境..."
	@docker-compose -f deploy/docker-compose.dev.yml up -d

# 停止所有服务
services-down:
	@echo "🛑 停止开发环境..."
	@docker-compose -f deploy/docker-compose.dev.yml down

# 查看日志
logs:
	@echo "📋 查看服务日志..."
	@docker-compose -f deploy/docker-compose.dev.yml logs -f

# 部署到 Kubernetes
deploy:
	@echo "☸️ 部署到 Kubernetes..."
	@kubectl apply -f deploy/k8s/

# 从 Kubernetes 删除
undeploy:
	@echo "🗑️ 从 Kubernetes 删除..."
	@kubectl delete -f deploy/k8s/

# 构建发布版本
release: clean
	@echo "🎯 构建发布版本..."
	@mkdir -p bin
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/nimbus-linux-amd64 cmd/nimbus/main.go
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/kce-ctl-linux-amd64 cmd/kce-ctl/main.go
	@CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/nimbus-darwin-amd64 cmd/nimbus/main.go
	@CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/kce-ctl-darwin-amd64 cmd/kce-ctl/main.go
	@CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/nimbus-windows-amd64.exe cmd/nimbus/main.go
	@CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -ldflags "$(LDFLAGS) -s -w" -o bin/kce-ctl-windows-amd64.exe cmd/kce-ctl/main.go
	@echo "✅ 发布版本构建完成!"

# 安装到系统路径
install: build
	@echo "📥 安装到系统..."
	@sudo cp bin/nimbus /usr/local/bin/
	@sudo cp bin/kce-ctl /usr/local/bin/
	@echo "✅ 安装完成!"

# 卸载
uninstall:
	@echo "🗑️ 卸载..."
	@sudo rm -f /usr/local/bin/nimbus
	@sudo rm -f /usr/local/bin/kce-ctl
	@echo "✅ 卸载完成!"

# 显示帮助信息
help:
	@echo "Nimbus 构建工具"
	@echo ""
	@echo "使用方法:"
	@echo "  make <目标>"
	@echo ""
	@echo "目标:"
	@echo "  build      构建项目"
	@echo "  test       运行测试"
	@echo "  benchmark  运行基准测试"
	@echo "  lint       代码质量检查"
	@echo "  fmt        格式化代码"
	@echo "  clean      清理构建文件"
	@echo "  deps       安装依赖"
	@echo "  docker     构建 Docker 镜像"
	@echo "  dev        开发模式"
	@echo "  swagger    生成 Swagger 文档"
	@echo "  migrate    运行数据库迁移"
	@echo "  services   启动开发环境"
	@echo "  services-down 停止开发环境"
	@echo "  logs       查看服务日志"
	@echo "  deploy     部署到 Kubernetes"
	@echo "  undeploy   从 Kubernetes 删除"
	@echo "  release    构建发布版本"
	@echo "  install    安装到系统"
	@echo "  uninstall  卸载"
	@echo "  help       显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build      # 构建项目"
	@echo "  make test       # 运行测试"
	@echo "  make docker     # 构建 Docker 镜像"
	@echo "  make dev        # 启动开发模式"