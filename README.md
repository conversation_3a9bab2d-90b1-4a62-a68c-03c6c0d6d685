# Nimbus 云平台 PaaS 框架

Nimbus 是一个基于 Kubernetes 的云平台 PaaS 框架，提供类似 EMR on K8s 或 ACK 的大数据平台功能。

## 功能特性

- 一键部署 Spark Operator 到用户的 K8s 集群
- 一键部署 Flink Operator 到用户的 K8s 集群
- 支持动态扩展更多大数据组件部署
- 基于用户创建的云上 k8s 集群 ID 进行操作
- 任务提交网关服务
- 集群管理和监控
- 支持工作流管理

## 技术栈

- Go 1.24.3
- Gin 框架作为 Web 服务器
- GORM 作为 ORM
- MySQL 作为数据库
- Kubernetes 作为底层容器编排平台
- Swagger 用于 API 文档

## 项目结构

```
nimbus/
├── cmd/                # 主程序入口
├── pkg/                # 公共库代码
├── internal/           # 内部业务逻辑
│   ├── api/            # API 路由和控制器
│   ├── service/        # 业务服务层
│   ├── model/          # 数据模型
│   ├── repository/     # 数据访问层
│   ├── kubernetes/     # K8s 客户端管理
│   └── workflow/       # 工作流管理
├── deploy/             # 部署相关文件
│   ├── k8s/            # K8s 部署文件
│   ├── helm/           # Helm Charts
│   └── docker/         # Docker 相关
├── scripts/            # 脚本文件
├── docs/               # 文档
├── test/               # 测试文件
└── configs/            # 配置文件
```

## 设计概念

### Operator 设计

系统使用工厂模式实现了灵活的 Operator 管理：

1. `Operator` 接口定义了所有 Operator 的通用行为
2. `OperatorFactory` 工厂类负责创建不同类型的 Operator 实例
3. 支持按需安装所需的 Operator，而不是全部安装
4. 易于扩展，可以添加更多类型的 Operator

### 集群管理

1. 用户创建集群时提供云上 k8s 集群 ID
2. 集群记录包含 AccountID 和 k8s 集群 ID
3. 不包含用户表和用户体系，这些由其他微服务处理

## 快速开始

### 前置条件

- Go 1.24.3 或更高版本
- MySQL 数据库
- Kubernetes 集群

### 安装

1. 克隆代码库

```bash
git clone https://github.com/kingsoft/nimbus.git
cd nimbus
```

2. 安装依赖

```bash
go mod download
```

3. 配置数据库

修改 `configs/config.yaml` 文件中的数据库配置：

```yaml
database:
  driver: "mysql"
  host: "your-db-host"
  port: 3306
  user: "your-username"
  password: "your-password"
  dbname: "nimbus"
```

4. 构建和运行

```bash
go build -o bin/nimbus ./cmd/nimbus
./bin/nimbus
```

### 本地调试

测试用例在 `test/` 目录下，可以通过以下命令运行：

```bash
# 运行测试（不安装Operator）
go test -v ./test/operator_test.go

# 运行测试（包括安装Operator）
RUN_OPERATOR_INSTALL_TEST=true go test -v ./test/operator_test.go
```

本地调试时会使用 `nimbus_v1` 数据库，可以使用 `2a9964c5-d3b9-4b95-8b32-5e752fcbb24a` 作为测试用的 k8s 集群 ID。

## API 文档

启动服务后，访问 Swagger API 文档：

```
http://localhost:8080/swagger/index.html
```

## 集群和 Operator 相关 API

### 集群管理

```
POST   /api/v1/clusters                 # 创建集群
GET    /api/v1/clusters                 # 获取集群列表
GET    /api/v1/clusters/:id             # 获取单个集群
PUT    /api/v1/clusters/:id             # 更新集群
DELETE /api/v1/clusters/:id             # 删除集群
POST   /api/v1/clusters/:id/validate    # 验证集群连接
```

### Operator 管理

```
POST   /api/v1/clusters/:id/operators                   # 安装 Operator
DELETE /api/v1/clusters/:id/operators/:operator_type    # 卸载 Operator
GET    /api/v1/clusters/:id/operators/:operator_type/status  # 获取 Operator 状态
```

## kce-ctl 小工具详细使用指南

kce-ctl 是 Nimbus 平台提供的命令行工具，类似于 kubectl，用于管理 KCE (Kingsoft Cloud Engine) 集群。

### 认证信息
- **用户名**: admin
- **密码**: kce-ctl@2025
- **会话有效期**: 1小时（自动保存会话，避免重复登录）

### 基础命令结构

```bash
kce-ctl [command] [resource-type] [resource-name] [flags]
```

### 常用全局参数

- `-c, --cluster`: KCE 集群 ID（必需）
- `-n, --namespace`: Kubernetes 命名空间（默认: default）
- `-o, --output`: 输出格式 (table, json, yaml)

### 资源查看命令 (get)

#### 查看节点信息
```bash
# 查看所有节点
sudo ./bin/kce-ctl get nodes -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看特定节点详情
sudo ./bin/kce-ctl get node <node-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 以 JSON 格式输出节点信息
sudo ./bin/kce-ctl get nodes -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -o json
```

#### 查看 Pod 信息
```bash
# 查看默认命名空间的所有 Pod
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看 kube-system 命名空间的 Pod
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n kube-system

# 查看特定 Pod
sudo ./bin/kce-ctl get pod <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n <namespace>

# 以 YAML 格式查看 Pod 详情
sudo ./bin/kce-ctl get pod <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -o yaml
```

#### 查看服务信息
```bash
# 查看所有服务
sudo ./bin/kce-ctl get services -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看特定命名空间的服务
sudo ./bin/kce-ctl get svc -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n kube-system

# 查看特定服务详情
sudo ./bin/kce-ctl get service <service-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

#### 查看部署信息
```bash
# 查看所有部署
sudo ./bin/kce-ctl get deployments -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看特定部署
sudo ./bin/kce-ctl get deploy <deployment-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看不同命名空间的部署
sudo ./bin/kce-ctl get deployments -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n production
```

#### 查看命名空间
```bash
# 查看所有命名空间
sudo ./bin/kce-ctl get namespaces -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看特定命名空间
sudo ./bin/kce-ctl get ns <namespace-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

### 资源详情命令 (describe)

```bash
# 查看 Pod 详细信息
sudo ./bin/kce-ctl describe pod <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n <namespace>

# 查看节点详细信息
sudo ./bin/kce-ctl describe node <node-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看服务详细信息
sudo ./bin/kce-ctl describe service <service-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 示例：查看特定 Pod 详情
sudo ./bin/kce-ctl describe pod vpc-cni-cgcjd -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n kube-system
```

### 日志查看命令 (logs)

```bash
# 查看 Pod 日志
sudo ./bin/kce-ctl logs <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 实时跟踪日志
sudo ./bin/kce-ctl logs <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -f

# 查看最近 50 行日志
sudo ./bin/kce-ctl logs <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a --tail=50

# 查看特定命名空间的 Pod 日志
sudo ./bin/kce-ctl logs <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n <namespace> -f
```

### 集群信息命令

```bash
# 查看集群基本信息
sudo ./bin/kce-ctl cluster-info -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

### Operator 管理命令

#### 查看 Operator 状态
```bash
# 列出所有 Operator
sudo ./bin/kce-ctl operator list -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看 Spark Operator 状态
sudo ./bin/kce-ctl operator status spark -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 查看 Flink Operator 状态
sudo ./bin/kce-ctl operator status flink -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

#### 安装 Operator
```bash
# 安装 Spark Operator
sudo ./bin/kce-ctl operator install spark -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 安装 Flink Operator
sudo ./bin/kce-ctl operator install flink -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

#### 卸载 Operator
```bash
# 卸载 Spark Operator
sudo ./bin/kce-ctl operator uninstall spark -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 卸载 Flink Operator
sudo ./bin/kce-ctl operator uninstall flink -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

### 资源管理命令

#### 创建资源
```bash
# 从文件创建资源
sudo ./bin/kce-ctl create -f deployment.yaml -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 应用资源配置
sudo ./bin/kce-ctl apply -f service.yaml -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

#### 删除资源
```bash
# 删除 Pod
sudo ./bin/kce-ctl delete pod <pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 删除服务
sudo ./bin/kce-ctl delete service <service-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 删除部署
sudo ./bin/kce-ctl delete deployment <deployment-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

### 实用工具命令

#### 自动补全
```bash
# 生成 Bash 自动补全脚本
sudo ./bin/kce-ctl completion bash > /usr/local/etc/bash_completion.d/kce-ctl

# 生成 Zsh 自动补全脚本
sudo ./bin/kce-ctl completion zsh > "${fpath[1]}/_kce-ctl"

# 生成 Fish 自动补全脚本
sudo ./bin/kce-ctl completion fish > ~/.config/fish/completions/kce-ctl.fish
```

#### 会话管理
```bash
# 退出登录（清除会话）
sudo ./bin/kce-ctl logout
```

### 常见使用场景示例

#### 场景1：检查集群健康状态
```bash
# 1. 查看集群信息
sudo ./bin/kce-ctl cluster-info -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 2. 查看所有节点状态
sudo ./bin/kce-ctl get nodes -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 3. 查看系统 Pod 状态
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n kube-system
```

#### 场景2：应用部署和监控
```bash
# 1. 查看应用部署状态
sudo ./bin/kce-ctl get deployments -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n production

# 2. 查看应用 Pod 状态
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n production

# 3. 查看应用日志
sudo ./bin/kce-ctl logs <app-pod-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n production -f

# 4. 查看服务状态
sudo ./bin/kce-ctl get services -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n production
```

#### 场景3：故障排查
```bash
# 1. 查看问题 Pod 详情
sudo ./bin/kce-ctl describe pod <problem-pod> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n <namespace>

# 2. 查看 Pod 日志
sudo ./bin/kce-ctl logs <problem-pod> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n <namespace> --tail=100

# 3. 查看节点资源状态
sudo ./bin/kce-ctl describe node <node-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

# 4. 查看相关事件（通过 describe 命令）
sudo ./bin/kce-ctl describe deployment <deployment-name> -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
```

### 故障排除

#### 网络连接问题
如果遇到 "警告: 清理hosts记录失败: 写入hosts文件失败: open /etc/hosts: permission denied" 错误：

1. **使用 sudo 权限运行**：
   ```bash
   sudo sudo ./bin/kce-ctl get nodes -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
   ```

2. **跳过 hosts 管理**（适用于服务器环境）：
   ```bash
   KCE_CTL_SKIP_HOSTS=true sudo ./bin/kce-ctl get nodes -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a
   ```

3. **手动添加 hosts 记录**：
   ```bash
   # 编辑 /etc/hosts 文件，添加以下记录
   sudo vim /etc/hosts
   
   # 添加内容：
   ************* appengine.inner.sdns.ksyun.com
   ************ 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a.k8spl-cn-beijing-6.kce.sdns.ksyun.com
   ```

#### 认证问题
- 会话过期时会自动提示重新登录
- 使用 `sudo ./bin/kce-ctl logout` 清除损坏的会话
- 测试环境可设置 `KCE_CTL_SKIP_AUTH=true` 跳过认证

#### 集群连接问题
- 确认集群 ID 正确
- 检查网络连接
- 验证 KCE 服务可用性

### 高级用法

#### 批量操作示例
```bash
# 查看多个命名空间的资源
for ns in default kube-system production; do
  echo "=== Namespace: $ns ==="
  sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n $ns
done

# 导出所有部署配置
sudo ./bin/kce-ctl get deployments -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -o yaml > all-deployments.yaml
```

#### 与其他工具结合
```bash
# 结合 jq 处理 JSON 输出
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -o json | jq '.items[].metadata.name'

# 结合 grep 过滤输出
sudo ./bin/kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a | grep Running
```

### 性能优化建议

1. **使用会话管理**：工具会自动保存1小时的登录会话，避免重复认证
2. **合理使用输出格式**：table 格式适合查看，json/yaml 格式适合脚本处理
3. **指定命名空间**：明确指定命名空间可以提高查询效率
4. **使用资源名称**：查询特定资源比列出所有资源更快

### 安全注意事项

- 工具会记录访问日志到 `~/.kce-ctl-access.log`
- 会话信息加密存储在 `~/.kce-ctl-session`
- 建议在生产环境中定期轮换访问凭据
- 使用完毕后可执行 `logout` 命令清除会话


## 扩展指南

### 添加新的 Operator 类型

1. 在 `kubernetes/operator.go` 中添加新的 OperatorType 常量
2. 实现新的 OperatorManager 结构体，满足 Operator 接口
3. 在 OperatorFactory 的 CreateOperator 方法中添加新的 case 分支

## 贡献

欢迎提交 Pull Request 或 Issue。

## 许可证

Apache License 2.0 