package logger

import (
	"fmt"
	"log/slog"
	"os"
	"strings"
)

var logger *slog.Logger

// InitLogger 初始化日志系统
func InitLogger(level string) {
	// 设置日志级别
	var logLevel slog.Level
	switch strings.ToLower(level) {
	case "debug":
		logLevel = slog.LevelDebug
	case "info":
		logLevel = slog.LevelInfo
	case "warn":
		logLevel = slog.LevelWarn
	case "error":
		logLevel = slog.LevelError
	default:
		logLevel = slog.LevelInfo
	}

	// 创建日志处理器
	opts := &slog.HandlerOptions{
		Level: logLevel,
	}
	handler := slog.NewJSONHandler(os.Stdout, opts)
	logger = slog.New(handler)

	// 设置为默认日志记录器
	slog.SetDefault(logger)
}

// Debug 记录调试级别日志
func Debug(msg string, args ...any) {
	logger.Debug(msg, args...)
}

// Info 记录信息级别日志
func Info(msg string, args ...any) {
	logger.Info(msg, args...)
}

// Warn 记录警告级别日志
func Warn(msg string, args ...any) {
	logger.Warn(msg, args...)
}

// Error 记录错误级别日志
func Error(msg string, args ...any) {
	logger.Error(msg, args...)
}

// WithFields 返回带有字段的新日志记录器
func WithFields(fields map[string]interface{}) *slog.Logger {
	attrs := make([]any, 0, len(fields)*2)
	for k, v := range fields {
		attrs = append(attrs, k, v)
	}
	return logger.With(attrs...)
}

// LogError 记录错误并返回格式化的错误
func LogError(err error, msg string) error {
	if err != nil {
		logger.Error(msg, "error", err)
		return fmt.Errorf("%s: %w", msg, err)
	}
	return nil
}
