package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// PodCleanupConfig Pod清理配置
type PodCleanupConfig struct {
	Enabled         bool          `yaml:"enabled" json:"enabled"`
	RetentionTime   time.Duration `yaml:"retention_time" json:"retention_time"`
	CleanupInterval time.Duration `yaml:"cleanup_interval" json:"cleanup_interval"`
	SyncInterval    time.Duration `yaml:"sync_interval" json:"sync_interval"`
	Namespaces      []string      `yaml:"namespaces" json:"namespaces"`
	LabelSelector   string        `yaml:"label_selector" json:"label_selector"`
}

// Config 应用配置
type Config struct {
	Server     ServerConfig     `yaml:"server" json:"server"`
	Database   DatabaseConfig   `yaml:"database" json:"database"`
	LogLevel   string           `yaml:"log_level" json:"log_level"`
	PodCleanup PodCleanupConfig `yaml:"pod_cleanup" json:"pod_cleanup"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port      int    `yaml:"port"`
	AlarmAddr string `yaml:"alarm_addr"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver   string `yaml:"driver"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
}

// KubernetesConfig Kubernetes配置
type KubernetesConfig struct {
	DefaultClusterID string `yaml:"default_cluster_id"`
}

// LoadConfig 从文件加载配置
func LoadConfig() (*Config, error) {
	// 默认配置
	config := &Config{
		Server: ServerConfig{
			Port: 8080,
		},
		Database: DatabaseConfig{
			Driver:   "mysql",
			Host:     "***********",
			Port:     8091,
			User:     "admin",
			Password: "CBd12399",
			DBName:   "nimbus_v1",
		},
		LogLevel: "info",
		PodCleanup: PodCleanupConfig{
			Enabled:         false,
			RetentionTime:   24 * time.Hour,
			CleanupInterval: 1 * time.Hour,
			SyncInterval:    10 * time.Minute,
			Namespaces:      []string{"default", "kube-system"},
			LabelSelector:   "app=nimbus-pod-cleanup",
		},
	}

	// 尝试从配置文件加载
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	_, err := os.Stat(configPath)
	if err == nil {
		file, err := os.Open(configPath)
		if err != nil {
			return nil, fmt.Errorf("无法打开配置文件: %w", err)
		}
		defer file.Close()

		decoder := yaml.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %w", err)
		}
	} else if !os.IsNotExist(err) {
		return nil, fmt.Errorf("检查配置文件状态失败: %w", err)
	}

	// 从环境变量覆盖配置
	if port := os.Getenv("SERVER_PORT"); port != "" {
		var p int
		if _, err := fmt.Sscanf(port, "%d", &p); err == nil {
			config.Server.Port = p
		}
	}

	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.Database.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		var p int
		if _, err := fmt.Sscanf(dbPort, "%d", &p); err == nil {
			config.Database.Port = p
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.Database.User = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.Database.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.Database.DBName = dbName
	}

	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		config.LogLevel = logLevel
	}

	return config, nil
}
