package utils

import (
	"encoding/json"
	"github.com/kingsoft/nimbus/pkg/config"
	"github.com/kingsoft/nimbus/pkg/logger"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"
)

var (
	alarmClient *AlarmClient
	once        sync.Once
)

type AlarmClient struct {
	alarmAddress string
}

const (
	SendAlarmPath = "/alarm/receptor"
)

func GetAlarmClient() *AlarmClient {
	cfg, err := config.LoadConfig()
	if err != nil {
		logger.Error("加载配置失败: %v", err)
	}
	if alarmClient == nil {
		once.Do(func() {
			alarmClient = &AlarmClient{
				alarmAddress: cfg.Server.AlarmAddr,
			}
		})
	}
	return alarmClient
}

func (a *AlarmClient) SendAlarm(data *AlarmData) error {
	header := a.NewHttpHeader(data.InstanceType, data.AccountId, data.Region)
	alarmReq := a.getAlarmReq(data)
	logger.Info("start send alarm with data: %v and requestId: %s", data, header["X-Ksc-Request-Id"])
	body, err := a.sendHttpRequest(SendAlarmPath, header, alarmReq)
	if err != nil {
		logger.Error("send http request failed, err: %s, instanceId: %s", err.Error(), data.InstanceId)
		return err
	}

	response := &HttpResponse{}
	err = json.Unmarshal(body, response)
	if err != nil {
		logger.Error("Unmarshal alarm http response err: %v", err)
		return err
	}
	logger.Info("alarm sdk response: %+v", response)
	return nil
}

func (a *AlarmClient) NewHttpHeader(t, accountId, region string) http.Header {
	var header http.Header = make(map[string][]string)
	header.Set("Content-Type", "application/json")
	header.Set("X-Ksc-Product", t)
	header.Set("X-Ksc-Account-Id", accountId)
	header.Set("X-Ksc-Source", "kmr")
	header.Set("X-Ksc-Region", region)
	header.Set("X-Ksc-Request-Id", string(UUID()))
	return header
}

func (a *AlarmClient) sendHttpRequest(url string, header http.Header, reqData any) ([]byte, error) {
	data, err := json.Marshal(reqData)
	if err != nil {
		return nil, err
	}
	logger.Info("AlarmClient url : [%s], reqData: %v", url, string(data))
	req, err := http.NewRequest("POST", a.alarmAddress+url, strings.NewReader(string(data)))
	if err != nil {
		return nil, err
	}
	req.Header = header
	resp, err := (&http.Client{}).Do(req)
	if err != nil {
		logger.Error("Http client initialization failed ,%s", err.Error())
		return nil, err
	}

	defer resp.Body.Close()
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	logger.Info("AlarmClient Get resp Data: %v", string(b))
	return b, nil
}

func (a *AlarmClient) getAlarmReq(data *AlarmData) *AlarmReqBody {
	alarmReq := &AlarmReqBody{
		Priority: P2,
	}

	alarmReq.Name = "名称"

	// 区分是否发送到线上告警群
	if !strings.Contains(data.Region, "beijing") || data.AccountId == "**********" {
		alarmReq.Product = Test
	} else if data.InstanceType == "线上" {
		alarmReq.Product = STARROCKS
	}

	content, htmlContent := a.getAlarmContent(data)
	alarmReq.Content = content
	alarmReq.HtmlContent = htmlContent

	return alarmReq
}

func (a *AlarmClient) getAlarmContent(data *AlarmData) (string, string) {
	// 生成 AlarmReqBody 的 Content 字段
	contentList := []string{
		"用户ID: " + data.AccountId,
		"集群ID: " + data.InstanceId,
		"集群名称: " + data.InstanceName,
		"集群类型: " + data.InstanceType,
		"地区: " + data.Region,
	}

	content := strings.Join(contentList, "; ")

	// 生成 AlarmReqBody 的 HtmlContent 字段
	contentList = append(contentList, "时间: "+time.Now().Format("2006.01.02 15:04:05"))

	htmlContent := strings.Join(contentList, "<br />")

	return content, htmlContent
}

type AlarmData struct {
	AccountId    string `json:"AccountId"`
	InstanceId   string `json:"InstanceId"`
	InstanceName string `json:"InstanceName"`
	Region       string `json:"Region"`
	InstanceType string `json:"InstanceType"`
	ErrInfo      string `json:"ErrInfo"`
}
type AlarmReqBody struct {
	Name        string       `json:"name"`
	Group       GroupType    `json:"group"`
	Product     ProductType  `json:"product"`
	Priority    PriorityType `json:"priority"`
	Content     string       `json:"content"`
	HtmlContent string       `json:"html_content"`
	Deal        DealType     `json:"no_deal"`
}

type HttpResponse struct {
	ErrCode   int         `json:"errcode"`
	ErrMsg    string      `json:"errmsg"`
	RequestId string      `json:"request_id"`
	Data      interface{} `json:"data"`
	Success   bool        `json:"success"`
}

type GroupType int

const (
	RD GroupType = 2
	OP GroupType = 4
)

// ProductType 报警标识, 具体来说，就是发送到哪个报警群
type ProductType string

// todo 修改成 uss 上配置的报警标识
const (
	Test      ProductType = "kkk"
	STARROCKS ProductType = "STARROCKS-MONITOR"
)

// PriorityType 报警级别
type PriorityType int

const (
	P0 PriorityType = 0
	P1 PriorityType = 1
	P2 PriorityType = 2
)

var PriorityTypeMap = map[string]PriorityType{
	"P0": P0,
	"P1": P1,
	"P2": P2,
}

// DealType 是否需要认领
type DealType int

const (
	Deal   DealType = 0
	NoDeal DealType = 1
)

type OperationState string

const (
	Init  OperationState = "开始"
	Done  OperationState = "完成"
	Error OperationState = "错误"
)
