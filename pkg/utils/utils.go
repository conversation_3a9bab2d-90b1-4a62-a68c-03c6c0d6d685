package utils

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	kce "ezone.ksyun.com/ezone/kce/kce-kubeconfig-client"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/pkg/common"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
)

// KCEAppEngineEndpoint KCE AppEngine服务地址
const KCEAppEngineEndpoint = "http://appengine.inner.sdns.ksyun.com:80"

// GetKubernetesConfig getKubernetesConfig 获取指定集群的Kubernetes配置
// 调用KCE服务的GetClusterKubeConfig接口获取集群的kubeconfig
// 参数:
//   - clusterId: 集群ID
//
// 返回:
//   - []byte: kubeconfig配置内容
//   - error: 错误信息
func GetKubernetesConfig(clusterId string) ([]byte, error) {
	ctx := context.Background()
	config, err := kce.GetClusterKubeConfig(ctx, clusterId, "kmr", KCEAppEngineEndpoint, "")
	return config, err
}

func MapKeys[T any](m map[string]T) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func String(a string) *string {
	return &a
}

func Bool(a bool) *bool {
	return &a
}

func Int64(a int64) *int64 {
	return &a
}

func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	rand.Seed(time.Now().UnixNano()) // 使用当前时间作为随机种子

	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))] // 从字符集中随机选择字符
	}
	return string(b)
}

func NewGinContextWithHeader(requestId string, accountId string, region string, accessGroupId string, subAccountId string) *gin.Context {
	if requestId == "" {
		requestId = uuid.NewString()
	}
	c := &gin.Context{}
	c.Request = new(http.Request)
	c.Request.Header = make(http.Header)
	c.Request.Header.Set("X-KSC-REQUEST-ID", requestId)
	c.Request.Header.Set("X-KSC-ACCOUNT-ID", accountId)
	c.Request.Header.Set("X-KSC-REGION", region)
	c.Request.Header.Set("X-KSC-ACCESS-GROUP-ID", accessGroupId)
	c.Request.Header.Set("X-KSC-USER-ID", subAccountId)
	c.Set("request_id", requestId)
	c.Set("account_id", accountId)
	c.Set("region", region)
	c.Set("real_id", subAccountId)
	return c
}

func FormatTimestampStr(timestamp string) string {
	intTimestamp, _ := strconv.ParseInt(timestamp, 10, 64)
	return FormatTimestampInt(intTimestamp)
}

func FormatTimestampInt(timestamp int64) string {
	var t time.Time
	timestampStr := fmt.Sprintf("%d", timestamp)
	length := len(timestampStr)

	switch length {
	case 10: // 秒级时间戳
		t = time.Unix(timestamp, 0)
	case 13: // 毫秒级时间戳
		t = time.Unix(timestamp/1000, 0)
	default:
		return ""
	}
	// 格式化时间为指定的字符串格式
	formattedTime := t.Format("2006-01-02 15:04:05")
	return formattedTime
}

func FormatTimeToStr(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func UUID() string {
	return uuid.New().String()
}

func CutUuid(uid string) string {
	return strings.Join(strings.Split(uid, "-")[0:2], "-")
}

func GetJobLatestStatus(job *batchv1.Job) string {
	// 对conditions按时间排序
	sort.Slice(job.Status.Conditions, func(i, j int) bool {
		return job.Status.Conditions[i].LastTransitionTime.Before(&job.Status.Conditions[j].LastTransitionTime)
	})

	// 取最新状态
	for i := len(job.Status.Conditions) - 1; i >= 0; i-- {
		if job.Status.Conditions[i].Status == corev1.ConditionTrue {
			return string(job.Status.Conditions[i].Type)
		}
	}
	return "Unknown"
}

// 工具函数：判断某个字符串是否在切片中
func Contains(list []string, target string) bool {
	for _, v := range list {
		if v == target {
			return true
		}
	}
	return false
}

// EnsureMillis 工具函数：确保时间戳为毫秒级
func EnsureMillis(ts int64) int64 {
	// 如果时间戳小于 10^12，认为是秒级时间戳，乘以 1000 转为毫秒
	if ts < 1e12 {
		return ts * 1000
	}
	return ts // 已经是毫秒
}

// EscapeContent 转义 content 中的特殊字符
func EscapeContent(content string) string {
	replacer := strings.NewReplacer(
		"%", "\\%",
		"_", "\\_",
		"\\", "\\\\",
		"'", "''",
	)
	return replacer.Replace(content)
}

// ToInterfaceSlice 将 []*T 转换为 []interface{}
func ToInterfaceSlice[T any](in []*T) []interface{} {
	result := make([]interface{}, len(in))
	for i, v := range in {
		result[i] = v
	}
	return result
}

// ConvertDuration 将毫秒转换为更易读的时间格式
func ConvertDuration(duration int64) string {
	const (
		hour   = 60 * 60 * 1000
		min    = 60 * 1000
		second = 1000
	)
	if duration > hour {
		return fmt.Sprintf("%.1f h", float64(duration)/float64(hour))
	} else if duration > min {
		return fmt.Sprintf("%.1f min", float64(duration)/float64(min))
	} else if duration > second {
		return fmt.Sprintf("%.1f s", float64(duration)/float64(second))
	}
	return fmt.Sprintf("%.1f ms", float64(duration))
}

// ConvertStorage 将字节转换为更易读的存储格式
func ConvertStorage(storage int64) string {
	const (
		bytesInKiB = 1024
		bytesInMiB = 1024 * 1024
		bytesInGiB = 1024 * 1024 * 1024
	)

	if storage >= bytesInGiB {
		return fmt.Sprintf("%.1f GiB", float64(storage)/float64(bytesInGiB))
	} else if storage >= bytesInMiB {
		return fmt.Sprintf("%.1f MiB", float64(storage)/float64(bytesInMiB))
	} else if storage >= bytesInKiB {
		return fmt.Sprintf("%.1f KiB", float64(storage)/float64(bytesInKiB))
	}
	return fmt.Sprintf("%.1f B", float64(storage))
}

func GetLogHistoryPath(clusterId string) string {
	uuidWithoutHyphens := strings.ReplaceAll(clusterId, "-", "")
	shortUUID := uuidWithoutHyphens[:10]
	return "w-" + shortUUID
}

/*
https://<clusterId>-sparkhistory.kmr-on-kce-pre.ksyun.com
*/
func GetSparkHistoryServerUrl(clusterId string) string {
	endpoint := "-sparkhistory.kmr-on-kce-pre.ksyun.com"
	// 使用第一个 - 分割的值
	parts := strings.Split(clusterId, "-")
	if len(parts) > 0 {
		return "https://" + parts[0] + endpoint
	}
	return "https://" + clusterId + endpoint
}

func GetJobWebUIUrl(jobId string) string {
	return "https://" + jobId + "-datajob.web-proxy.ksyun.com"
}

func GetJobWebUIHost(jobId string) string {
	return jobId + "-datajob.web-proxy.ksyun.com"
}

// bigdata_accountId_region

func GetHistoryBucketName(accountId, region string) string {
	return "kaic-bigdata-" + accountId + "-" + region
}

func GetJobTypeByJobId(jobId string) common.JobType {
	switch {
	case strings.Contains(jobId, "spark"):
		return common.JobTypeSpark
	case strings.Contains(jobId, "ray"):
		return common.JobTypeRay
	case strings.Contains(jobId, "flink"):
		return common.JobTypeFlink
	default:
		return common.JobTypeSync
	}
}
