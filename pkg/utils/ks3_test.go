/**
 * <AUTHOR>
 * @date 2025/6/5 14:10
 * @description ks3_test
 */
package utils

import (
	"fmt"
	"testing"
)

// ak, sk, ks3Region, endPoint, bucket string
/**
LogHistoryBucket = "bigdata_platform"
	LogHistoryPath   = "spark/"
*/
func TestKS3CreatePath(t *testing.T) {
	ak := "AKLT5k45XEaBSJ-Nv3o0QG0Q5A"
	sk := "OHRmsdngl4T7uICvcxJixTSayk7YOtGvFhofh+feeIRM9XTe07kLP8x/YIDUEproTg=="
	bucket := "kaic-bigdata-2000003485-cn-beijing-6"
	path := "w-a4476a5533/"
	region := "cn-beijing-6"

	err := EnsureKS3Path(ak, sk, region, bucket, path)
	if err != nil {
		t.Errorf("EnsureKS3Path failed: %v", err)
		return
	}
}

func TestCheckExists(t *testing.T) {
	ak := "AKLT2Hriis9ZRJWSY6wsCIZt"
	sk := "OLe3X1AmmJFwjUKrTmjRv700FfXgM0A5kjZQN8Uu"
	bucket := "spark-aaa"

	region := "cn-northwest-2"
	endPoint, _, _ := GetKs3EndPointByRegion(region)
	exist, err := HeadBucketExist(ak, sk, "QINGYANGPRE", endPoint, bucket)
	fmt.Printf("ks3Region: %v, endPoint: %v", "QINGYANGPRE", endPoint)
	if err != nil {
		t.Errorf("EnsureKS3Path failed: %v", err)
	}
	//err := CreateBucket(ak, sk, "QINGYANGPRE", endPoint, bucket)
	//if err != nil {
	//	t.Errorf("EnsureKS3Path failed: %v", err)
	//	return
	//}
	t.Logf("bucket exist: %v", exist)
}
