package utils

import (
	"errors"
	"fmt"
	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/ks3sdklib/aws-sdk-go/aws"
	"github.com/ks3sdklib/aws-sdk-go/aws/credentials"
	"github.com/ks3sdklib/aws-sdk-go/service/s3"
	"net/http"
	"regexp"
	"strings"
	"time"
)

func PathsValidate(paths []string, ak, sk, region string) error {
	for _, path := range paths {
		// 空路径不处理
		if path == "" {
			continue
		}
		// 本地路径不处理
		if strings.HasPrefix(path, "local://") {
			continue
		}

		// 验证ks3路径
		bucket, key, isKs3 := ParseS3Path(path)
		if !isKs3 {
			return fmt.Errorf("invalid S3 or KS3 path: %v", path)
		}
		if err := IsFileExist(bucket, key, ak, sk, region); err != nil {
			return err
		}

	}
	return nil
}

func IsFileExist(bucket, key, ak, sk, region string) error {
	if bucket == "" || key == "" || ak == "" || sk == "" {
		return fmt.Errorf("无效输入: bucket, key, ak 或 sk 为空")
	}
	cre := credentials.NewStaticCredentials(ak, sk, "")
	endPoint, region, err := GetKs3EndPointByRegion(region)
	if err != nil {
		return err
	}
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
		HTTPClient:  &http.Client{Timeout: 3 * time.Second},
		MaxRetries:  1,
	})
	// 判断文件是否存在
	resp, err := client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(bucket), // 存储空间名称，必填
		Key:    aws.String(key),    // 对象的key，必填
	})
	if err != nil {
		if resp.StatusCode == nil {
			return errors.New(fmt.Sprintf("检测到您的作业依赖非北京区域的 KS3 资源: %v，这可能导致运行失败，建议资源迁移至北京区域.", bucket+"/"+key))
		}
		if *resp.StatusCode == 404 {
			return fmt.Errorf("文件不存在: %s", bucket+"/"+key)
		} else if *resp.StatusCode == 403 {
			return errors.New(fmt.Sprintf("ak,sk 不存在"))
		}
		return fmt.Errorf("ks3 error: %s", err)
	}
	return nil
}

// ParseS3Path 提取存储桶名称和对象键
func ParseS3Path(path string) (bucket, key string, isKs3 bool) {
	const s3Prefix = "s3://"
	const ks3Prefix = "ks3://"

	// 检查路径是否以 s3:// 或 ks3:// 开头
	if strings.HasPrefix(path, s3Prefix) {
		path = strings.TrimPrefix(path, s3Prefix)
	} else if strings.HasPrefix(path, ks3Prefix) {
		path = strings.TrimPrefix(path, ks3Prefix)
	} else {
		return "", "", false
	}
	// 分割路径以获取存储桶名称和对象键
	parts := strings.SplitN(path, "/", 2)
	if len(parts) != 2 {
		// 如果分割结果不是两部分，则返回空字符串
		return "", "", false
	}

	bucket, key = parts[0], parts[1]
	return bucket, key, true
}

// MergeSlices 将多个字符串切片合并为一个切片
func MergeSlices(slices ...[]string) []string {
	var result []string
	for _, slice := range slices {
		result = append(result, slice...)
	}
	return result
}

// 根据cluster部署的region，返回对应的ks3 endpoint和ks3 region
// https://docs.ksyun.com/documents/6761
// http://wiki.op.ksyun.com/pages/viewpage.action?pageId=21302996
// todo 不全
func GetKs3EndPointByRegion(region string) (Ks3EndPoint string, Ks3Region string, err error) {
	if region == "cn-northwest-1" {
		return "ks3-cn-qingyang-internal.ksyuncs.com", "QINGYANG", nil
	}
	if region == "cn-northwest-3" {
		return "ks3-cn-ningxia-internal.ksyuncs.com", "NINGXIA", nil
	}
	if region == "cn-northwest-4" {
		return "ks3-cn-qinghai-internal.ksyuncs.com", "QINGHAI", nil
	}
	// 转小写，并去掉数字
	s := strings.ToLower(region)
	re := regexp.MustCompile(`\d`)
	s = string(re.ReplaceAll([]byte(s), nil))

	switch s {
	case "cn-beijing-":
		return "ks3-cn-beijing-internal.ksyuncs.com", "BEIJING", nil
	case "cn-shanghai-":
		return "ks3-cn-shanghai-internal.ksyuncs.com", "SHANGHAI", nil
	case "cn-guangzhou-", "pre-online":
		return "ks3-cn-guangzhou-internal.ksyuncs.com", "GUANGZHOU", nil
	case "cn-qingdao-":
		return "ks3-cn-qingdao-internal.ksyuncs.com", "QINGDAO", nil
	case "cn-hongkong-":
		return "ks3-cn-hk-1-internal.ksyuncs.com", "HONGKONG", nil
	case "ap-singapore-":
		return "ks3-sgp-internal.ksyuncs.com", "SINGAPORE", nil
	case "eu-east-":
		return "ks3-rus-internal.ksyuncs.com", "RUSSIA", nil
	// 金融专区（北京）
	case "cn-beijing-fin":
		return "ks3-jr-beijing-internal.ksyuncs.com", "JR_BEIJING", nil
	// 金融专区（上海）
	case "cn-shanghai-fin":
		return "ks3-jr-shanghai-internal.ksyuncs.com", "JR_SHANGHAI", nil
	// 政务专区（北京）
	case "cn-north-gov":
		return "ks3-gov-beijing-internal.ksyuncs.com", "GOV_BEIJING", nil
	// 台北
	case "cn-taipei-":
		return "ks3-taiwan-internal.ksyuncs.com", "TAIWAN", nil
	// 华北专属1区
	case "cn-north-vip":
		return "ks3-cn-tianjin-xm01-internal.ksyuncs.com", "CNNorthVIP1Region", nil
	//庆阳测试
	case "cn-qingyangtest-":
		return "ks3-cn-qingyangtest-internal.ksyuncs.com", "QINGYANG", nil
	case "cn-ningbo-":
		return "ks3-cn-ningbo-internal.ksyuncs.com", "NINGBO", nil

	// 西北二（自用）
	case "cn-northwest-":
		return "ks3-cn-qingyang-pre-internal.ksyuncs.com", "QINGYANGPRE", nil
	default:
		return "", "", fmt.Errorf("GetKs3EndPointByRegion: unknown ks3 region: %s", region)
	}
}

func HeadBucketExist(ak, sk, region, endPoint, bucket string) (bool, error) {
	// 创建访问凭证，请将<AccessKeyID>与<SecretAccessKey>替换成真正的值
	cre := credentials.NewStaticCredentials(ak, sk, "")
	// 创建S3Client，更多配置项请查看Go-SDK初始化文档
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
		MaxRetries:  3,
	})
	// 获取存储空间访问权限
	exist, err := client.HeadBucketExist(bucket)
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			return false, nil
		} else {
			return false, err
		}
	}
	return exist, nil
}

func CreateBucket(ak, sk, region, endPoint, bucket string) error {
	// 创建访问凭证，请将<AccessKeyID>与<SecretAccessKey>替换成真正的值
	cre := credentials.NewStaticCredentials(ak, sk, "")
	// 创建S3Client，更多配置项请查看Go-SDK初始化文档
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
		MaxRetries:  3,
	})
	// 创建存储空间
	_, err := client.CreateBucket(&s3.CreateBucketInput{
		Bucket: aws.String(bucket),        // 存储空间名称，必填
		ACL:    aws.String(s3.ACLPrivate), // 存储空间访问权限，非必填
	})
	if err != nil {
		return fmt.Errorf("创建存储空间失败: %s", err)
	}
	return nil
}

func HeadObject(ak, sk, region, endPoint, bucket, prefix string) (*s3.HeadObjectOutput, error) {
	cre := credentials.NewStaticCredentials(ak, sk, "")
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
	})
	resp, err := client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(bucket), // 存储空间名称，必填
		Key:    aws.String(prefix),
	})
	return resp, err
}

func PutObject(ak, sk, region, endPoint, bucket, path string) error {
	cre := credentials.NewStaticCredentials(ak, sk, "")
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
		MaxRetries:  3,
	})
	_, err := client.PutObject(&s3.PutObjectInput{
		Bucket: aws.String(bucket), // 存储空间名称，必填
		Key:    aws.String(path),
	})
	if err != nil {
		return err
	}

	return nil
}

func EnsureKS3Path(ak, sk, region, bucket, path string) error {
	endPoint, ks3Region, err := GetKs3EndPointByRegion(region)
	if err != nil {
		return fmt.Errorf("get ks3 endpoint failed: %w", err)
	}

	// 如果文件已存在，直接返回
	if err = IsFileExist(bucket, path, ak, sk, region); err == nil {
		fmt.Println("file already exists")
		return nil
	}

	// 检查 bucket 是否存在
	exist, err := HeadBucketExist(ak, sk, ks3Region, endPoint, bucket)
	if err != nil {
		return fmt.Errorf("check bucket exist failed: %w", err)
	}

	if !exist {
		// 创建 bucket
		if err = CreateBucket(ak, sk, ks3Region, endPoint, bucket); err != nil {
			return fmt.Errorf("create bucket failed: %w", err)
		}
	}

	// 创建对象
	if err = PutObject(ak, sk, ks3Region, endPoint, bucket, path+"/"); err != nil {
		return fmt.Errorf("put object (create path) failed: %w", err)
	}

	return nil
}

func ListBuckets(ak, sk, region string) ([]string, error) {
	// 创建访问凭证，请将<AccessKeyID>与<SecretAccessKey>替换成真正的值
	endPoint, _, _ := GetKs3EndPointByRegion(region)
	logger.Info("ListBuckets:region %s, endPoint %s", region, endPoint)
	cre := credentials.NewStaticCredentials(ak, sk, "")
	// 创建S3Client，更多配置项请查看Go-SDK初始化文档
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
	})
	// 获取存储空间列表
	resp, err := client.ListBuckets(&s3.ListBucketsInput{})
	if err != nil {
		return nil, err
	}

	var results []string
	for _, bucket := range resp.Buckets {
		if *bucket.Region != region {
			continue
		}
		results = append(results, *bucket.Name)
	}
	return results, nil
}

func ListObject(ak, sk, region, bucket, prefix string) (*s3.ListObjectsOutput, error) {
	// 创建访问凭证，请将<AccessKeyID>与<SecretAccessKey>替换成真正的值
	cre := credentials.NewStaticCredentials(ak, sk, "")
	endPoint, _, _ := GetKs3EndPointByRegion(region)
	// 创建S3Client，更多配置项请查看Go-SDK初始化文档
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
	})
	// 获取存储空间访问权限
	resp, err := client.ListObjects(&s3.ListObjectsInput{
		Bucket: aws.String(bucket), // 存储空间名称，必填
		Prefix: aws.String(prefix),
	})
	return resp, err

}

func DeleteBucketPrefix(ak, sk, region, bucket, path string) error {
	cre := credentials.NewStaticCredentials(ak, sk, "")
	endPoint, _, _ := GetKs3EndPointByRegion(region)
	client := s3.New(&aws.Config{
		DisableSSL:  true,
		Credentials: cre,      // 访问凭证
		Region:      region,   // 填写您的Region
		Endpoint:    endPoint, // 填写您的Endpoint
	})
	_, err := client.DeleteBucketPrefix(&s3.DeleteBucketPrefixInput{
		Bucket: aws.String(bucket), // 存储空间名称，必填
		Prefix: aws.String(path),
	})
	if err != nil {
		return err
	}

	return nil
}
