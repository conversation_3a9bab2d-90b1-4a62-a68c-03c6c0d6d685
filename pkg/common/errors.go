package common

import "fmt"

type ErrorType string

const (
	ErrorTypeUser     ErrorType = "ErrorTypeUser"     // 用户侧错误，如错误的请求参数（400）
	ErrorTypeAuth     ErrorType = "ErrorTypeAuth"     // 认证错误（403）
	ErrorTypeNotFound ErrorType = "ErrorTypeNotFound" // 未找到 (404)
	ErrorTypeBusy     ErrorType = "ErrorTypeBusy"     // 服务繁忙（429）
	ErrorTypeInternal ErrorType = "ErrorTypeInternal" // 服务端错误（500）
)

func (t ErrorType) String() string {
	return string(t)
}

type Error struct {
	Type    ErrorType // 错误分类，用于中间件的判断等
	Code    Code      // 返回给用户的业务错误码
	Message string    // 返回给用户的用户友好消息
	Detail  any       // 详情，比如一个error，不返回给用户，仅打印日志
}

func (e *Error) Error() string {
	if e.Detail == nil {
		return ""
	}
	switch d := e.Detail.(type) {
	case *Error:
		return d.Error()
	case error:
		return d.Error()
	default:
		return fmt.Sprintf("%+v", d)
	}
}

// NewError 创建一个错误，该错误会被中间件处理，打印恰当的日志，返回给用户恰当的信息。controller和service不必打印它的日志。
func NewError(errorType ErrorType, code Code, message string, detail any) *Error {
	if message == "" {
		message = string(code)
	}
	return &Error{
		Type:    errorType,
		Code:    code,
		Message: message,
		Detail:  detail,
	}
}

// 常见错误的便利函数如下，不常见的错误请直接调用NewError

func NewUserError(code Code, message string, detail any) *Error {
	return NewError(ErrorTypeUser, code, message, detail)
}

func NewParamError(message string, detail any) *Error {
	return NewError(ErrorTypeUser, CodeInvalidParams, message, detail)
}

func NewNotFoundError(message string, detail any) *Error {
	return NewError(ErrorTypeNotFound, CodeNotFound, message, detail)
}

func NewInternalError(code Code, message string, detail any) *Error {
	return NewError(ErrorTypeInternal, code, message, detail)
}
