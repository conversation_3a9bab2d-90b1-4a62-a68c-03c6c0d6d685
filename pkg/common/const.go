package common

type JobType string

const (
	ClusterCommon     ClusterType = "common"
	ClusterServerless ClusterType = "serverless"

	JobTypeSpark JobType = "spark"
	JobTypeFlink JobType = "flink"
	JobTypeRay   JobType = "ray"
	JobTypeSync  JobType = "sync"

	HeaderAccountId  = "x-ksc-account-id"
	HeaderRegion     = "x-ksc-region"
	HeaderRequestId  = "x-ksc-request-id"
	HeaderRequestId1 = "RequestId"
)

type ClusterType string

type AccessType int32

const (
	AccessCreatorOnly  AccessType = 0
	AccessQueueVisible AccessType = 1
)

func (o JobType) IsValid() bool {
	switch o {
	case JobTypeSpark, JobTypeFlink, JobTypeRay, JobTypeSync:
		return true
	}
	return false
}

type Priority string

const (
	P0 Priority = "P0"
	P1 Priority = "P1"
	P2 Priority = "P2"
)

func (o Priority) IsValid() bool {
	switch o {
	case P0, P1, P2:
		return true
	}
	return false
}

var ServerlessJobPriority = map[Priority]bool{
	P0: true,
	P1: false,
	P2: false,
}

const (
	PriorityNameHigh   string = "kaic-high"   // 高优先级 P0
	PriorityNameNormal string = "kaic-normal" // 正常优先级 P1
	PriorityNameLow    string = "kaic-low"    // 低优先级 P2
)

var CommonJobPriority = map[Priority]string{
	P0: PriorityNameHigh,
	P1: PriorityNameNormal,
	P2: PriorityNameLow,
}

const (
	ServerlessSparkVersion = "3.4.3"
	ServerlessRayVersion   = "2.30.0"
	ServerlessFlinkVersion = "v1_19"
)

func (o ClusterType) IsValid() bool {
	switch o {
	case ClusterCommon, ClusterServerless:
		return true
	}
	return false
}

type SyncType string

const (
	KpfsToKs3  SyncType = "kpfs_to_ks3"
	Ks3ToKpfs  SyncType = "ks3_to_kpfs"
	Ks3ToKs3   SyncType = "ks3_to_ks3"
	KpfsToKpfs SyncType = "kpfs_to_kpfs"
)

type PodType string

const (
	DriverType      PodType = "driver"
	ExecutorType    PodType = "executor"
	HeadType        PodType = "head"
	JobManagerType  PodType = "jobmanager"
	TaskManagerType PodType = "taskmanager"
)

type LogSource string

const (
	FileType LogSource = "file"
	StdType  LogSource = "std"
)

const (
	KLOG_FIELD_LOG   = "message"
	KLOG_FIELD_ORDER = "__id__"
)
