package common

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
)

type HttpResponse struct {
	Code      Code   `json:"Code"`
	Message   string `json:"Message"`
	RequestId string `json:"RequestId"`
	Data      any    `json:"Data,omitempty"`
}

func ErrorHttpResponse(c *gin.Context, code Code, err error) {
	c.JSON(http.StatusBadRequest, HttpResponse{
		Code:      code,
		Message:   fmt.Sprintf("%s, %s", CodeMsgMap[code], err.Error()),
		RequestId: GetRequestIDFromContext(c),
	})
}

func SuccessHttpResponse(c *gin.Context, data any) {
	c.JSON(http.StatusOK, HttpResponse{
		Code:      CodeSuccess,
		RequestId: GetRequestIDFromContext(c),
		Data:      data,
	})
}
