package common

import (
	"context"
)

func GetRequestIDFromContext(c context.Context) string {
	v := c.Value("request_id")
	if requestId, ok := v.(string); ok {
		return requestId
	}
	return ""
}

func GetAccountIdFromContext(c context.Context) string {
	v := c.Value("account_id")
	if accountId, ok := v.(string); ok {
		return accountId
	}
	return ""
}

func GetRegionFromContext(c context.Context) string {
	v := c.Value("region")
	if region, ok := v.(string); ok {
		return region
	}
	return ""
}

func GetSubAccountIdFromContext(c context.Context) string {
	v := c.Value("real_id")
	if realId, ok := v.(string); ok {
		return realId
	}
	return ""
}

func GetRealIdFromContext(c context.Context) string {
	subAccountId := GetSubAccountIdFromContext(c)
	if subAccountId != "" {
		return subAccountId
	}
	return GetAccountIdFromContext(c)
}
