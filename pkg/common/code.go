package common

type Code string

const (
	CodeSuccess            Code = "OK"
	CodeInvalidParams      Code = "InvalidParams"
	CodeParamValidateError Code = "ParamValidateError"
	CodeInternalError      Code = "InternalError"
	CodeUnauthorized       Code = "Unauthorized"
	CodeForbidden          Code = "Forbidden"
	CodeNotFound           Code = "NotFound"
	CodeAlreadyExists      Code = "AlreadyExists"
	CodeLimitExceeded      Code = "LimitExceeded"
	CodeInvalidJobType     Code = "InvalidJobType"
)

var CodeMsgMap = map[Code]string{
	CodeSuccess: "成功",
}

var ServerlessErrorCodeMap = map[int]string{
	0:  "OK",
	1:  "BodyParseFailed",
	2:  "ErrorWorkspace",
	3:  "DbSystemError",
	4:  "GetResourceError",
	5:  "PutResourceError",
	6:  "DeleteResourceError",
	7:  "ParseArgsError",
	8:  "InitSparkApplicationDataError",
	9:  "CreateSparkApplicationError",
	10: "QueueNameExist",
	11: "CreateQueueError",
	12: "QueueNameNotExist",
	13: "QueueNotBelongWorkspace",
	14: "GetSparkLogError",
	15: "InitWorkspaceError",
	16: "SystemError",
	17: "SubmitJobParamsError",
	18: "ResourceNotEnough",
	19: "WorkspaceHaveApplicationRunning",
	20: "OrderSystemError",
	21: "WorkspaceStatusError",
	22: "InvalidParamsWorkspaceName",
	23: "InvalidParamsResourceSpec",
	24: "WorkspaceNotFound",
	25: "WorkspaceNotRunning",
	26: "GetSparkLogInvalid",
	27: "BucketPolicyError",
	28: "ApplicationNotFound",
	29: "ApplicationIsDeleted",
	30: "CreateKfsMountPathError",
	31: "ChangeKs3PathToHttpPathLocalPathError",
	32: "AddCloudMonitorFailed",
	33: "GetSparkApplicationError",
	34: "GetExecutorListError",
	35: "CannotPerformThisOperation",
	36: "WorkspaceHasWaitingJob",
	37: "InitRayJobDataError",
	38: "CreateRayJobError",
	39: "GetSparkApplicationStartupLogError",
	40: "InvalidPodNameType",
	41: "InvalidPodFileType",
	42: "CreateJuiceFsError",
	43: "InvalidProductType",
	44: "CreateKpfsError",
	45: "KpfsNotFound",
	46: "KpfsSyncInitError",
	47: "LogDownloadLimitExceeded",
	48: "BuildLogQueryFactoryFailed",
	49: "CreateDownloadTasksToKlogError",
	50: "ListDownloadTasksToKlogError",
	51: "StopTaskToKlogError",
	52: "KpfsInvalidParams",
	53: "InitFlinkJobDataError",
	54: "JobNotRunning",
	55: "SuspendJobError",
	56: "RunningJobError",
	57: "JobNotSuspended",
	58: "GetAccountQuotaFailed",
	59: "CheckOrAddAccountQuotaFailed",
	60: "GetComputeClusterError",
	61: "CreateFlinkDepError",
}
