apiVersion: "sparkoperator.k8s.io/v1beta2"
kind: SparkApplication
metadata:
  name: spark-pi
  namespace: kmrspark
spec:
  type: Scala
  mode: cluster
  #image: "gcr.io/spark-operator/spark:v3.1.1"
  image: "hub.kce.ksyun.com/bigdata-platform/pyspark:v3.4.3-ksc1.3"
  imagePullPolicy: IfNotPresent
  hadoopConf:
      fs.ks3.AccessKey: AKLT5k45XEaBSJ-Nv3o0QG0Q5A
      fs.ks3.AccessSecret: OHRmsdngl4T7uICvcxJixTSayk7YOtGvFhofh+feeIRM9XTe07kLP8x/YIDUEproTg==
      fs.ks3.check.subdir.in.getfilestatus: "true"
      fs.ks3.endpoint: ks3-cn-beijing-internal.ksyuncs.com
      mapreduce.fileoutputcommitter.algorithm.version: "2"
      mapreduce.fileoutputcommitter.task.cleanup.enabled: "true"
  sparkConf:
      spark.driver.extraJavaOptions: -Duser.timezone=Asia/Shanghai
      spark.dynamicAllocation.executorIdleTimeout: "300"
      spark.eventLog.dir: ks3://zhangbo/spark/helm-test/w-2a9964c5d3
      spark.eventLog.enabled: "true"
      spark.executor.extraJavaOptions: -Duser.timezone=Asia/Shanghai
      spark.executor.processTreeMetrics.enabled: "true"
      spark.kubernetes.driver.annotation.prometheus.io/path: /metrics/executors/prometheus/
      spark.kubernetes.driver.annotation.prometheus.io/port: "4040"
      spark.kubernetes.driver.annotation.prometheus.io/scrape: "true"
      spark.kubernetes.driver.reusePersistentVolumeClaim: "false"
      spark.kubernetes.driver.service.deleteOnTermination: "false"
  mainClass: org.apache.spark.examples.SparkPi
  mainApplicationFile: "local:///opt/spark/examples/jars/spark-examples_2.12-3.4.3.jar"
  sparkVersion: "3.4.3"
  restartPolicy:
    type: Never
  volumes:
    - name: "test-volume"
      hostPath:
        path: "/tmp"
        type: Directory
  driver:
    cores: 1
    coreLimit: "1200m"
    memory: "512m"
    labels:
      version: 3.4.3
    serviceAccount: kmrspark
    volumeMounts:
      - name: "test-volume"
        mountPath: "/tmp"
  executor:
    cores: 1
    instances: 1
    memory: "512m"
    labels:
      version: 3.1.3
    volumeMounts:
      - name: "test-volume"
        mountPath: "/tmp"
