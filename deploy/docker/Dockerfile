# 构建阶段
FROM golang:1.24-alpine AS builder

WORKDIR /app

# 复制 go.mod 和 go.sum
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 编译
RUN CGO_ENABLED=0 GOOS=linux go build -o nimbus ./cmd/nimbus

# 运行阶段
FROM alpine:latest

WORKDIR /app

# 安装依赖
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 复制编译好的二进制文件
COPY --from=builder /app/nimbus /app/
COPY --from=builder /app/configs /app/configs

# 暴露端口
EXPOSE 8080

# 运行
CMD ["/app/nimbus"] 