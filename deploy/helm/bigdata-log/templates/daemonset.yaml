apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ .Values.name }}
spec:
  selector:
    matchLabels:
      name: bigdata-filebeat
  template:
    metadata:
      labels:
        name: bigdata-filebeat
    spec:
      serviceAccountName: filebeat-operator-spark
      containers:
        - name: filebeat
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - name: log-volume
              mountPath: /var/log/serverless/std
            - name: filebeat-data
              mountPath: /usr/share/filebeat/data
            - name: config-filebeat
              mountPath: /usr/share/filebeat/filebeat.yml
              subPath: filebeat.yml
            - name: config-credential
              mountPath: /usr/share/filebeat/credential.ini
              subPath: credential.ini
            - name: tz-config
              mountPath: /etc/localtime
          securityContext:
            privileged: true
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      tolerations:
        - operator: Exists
      volumes:
        - name: log-volume
          hostPath:
            path: /var/log/pods
            type: Directory
        - name: filebeat-data
          hostPath:
            path: /var/filebeat-data
            type: DirectoryOrCreate
        - name: config-filebeat
          configMap:
            name: {{ .Values.name }}-config
        - name: config-credential
          configMap:
            name: {{ .Values.name }}-ini
        - name: tz-config
          hostPath:
            path: /etc/localtime
