apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: filebeat-spark-role

rules:
  - apiGroups: [""]
    resources:
      - pods
      - services
      - configmaps
      - persistentvolumeclaims
    verbs: ["*"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: filebeat-spark
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: filebeat-spark-role
subjects:
  - kind: ServiceAccount
    name: filebeat-operator-spark

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: filebeat-operator-spark