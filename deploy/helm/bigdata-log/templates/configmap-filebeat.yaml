apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.name }}-config

data:
  filebeat.yml: |-
    path.data: /usr/share/filebeat/data

    filebeat.inputs:
      - type: container
        enabled: true
        paths:
          - /var/log/serverless/std/bigdata_pyspark-*/*/*.log
          - /var/log/serverless/std/bigdata_*-exec-*/*/*.log
          - /var/log/serverless/std/bigdata_spark-java-*/*/*.log
          - /var/log/serverless/std/bigdata_rayjob-*/*/*.log
        fields_under_root: true
        close_inactive: 1m
        close_timeout: 2m
        clean_removed: true
        scan_frequency: 1m
        fields:
          node: ${NODE_NAME}
          file_type: std
        processors:
          - dissect:
              tokenizer: "/var/log/serverless/std/%{namespace}_%{pod_name}_%{pod_uid}/%{container_name}/%{filename}"
              field: "log.file.path"
              target_prefix: ""
          - if:
              contains:
                container_name: spark
            then:
              - add_fields:
                  target: ""
                  fields:
                    product: spark
            else:
              - if:
                  or:
                    - contains:
                        container_name: ray
                    - equals:
                        container_name: wait-gcs-ready
                then:
                  - add_fields:
                      target: ""
                      fields:
                        product: ray
                else:
                  - add_fields:
                      target: ""
                      fields:
                        product: unknown

    # =============== processors =================
    processors:
      - if:
          equals:
            product: ray
        then:
          - grok:
              source_field: pod_name
              target_field: "grok"
              pattern: '%{job_name_rg:job_name}'
              patterns:
                job_name_rg: 'rayjob-\d+-\w+'
          - copy_fields:
              fields:
                - from: grok.job_name
                  to: job_name
              fail_on_error: false
              ignore_missing: true
          - drop_fields:
              fields: ["grok"]
              ignore_missing: true
          - if:
              contains:
                pod_name: -ray-head
            then:
              - add_fields:
                  target: ""
                  fields:
                    pod_type: head
            else:
              - if:
                  contains:
                    pod_name: -worker-
                then:
                  - add_fields:
                      target: ""
                      fields:
                        pod_type: worker
                else:
                  - add_fields:
                      target: ""
                      fields:
                        pod_type: job

        else:
          - if:
              equals:
                product: flink
            then:
              - grok:
                  source_field: pod_name
                  target_field: "grok"
                  pattern: '%{job_name_rg:job_name}'
                  patterns:
                    job_name_rg: 'flinkdep-\d+-\w+'
              - copy_fields:
                  fields:
                    - from: grok.job_name
                      to: job_name
                  fail_on_error: false
                  ignore_missing: true
              - drop_fields:
                  fields: ["grok"]
                  ignore_missing: true

              - if:
                  contains:
                    pod_name: taskmanager
                then:
                  - add_fields:
                      target: ""
                      fields:
                        pod_type: taskmanager
                else:
                  - add_fields:
                      target: ""
                      fields:
                        pod_type: jobmanager
            else:
              - if:
                  contains:
                    product: spark
                then:
                  - grok:
                      source_field: pod_name
                      target_field: "grok"
                      pattern: '%{job_name_rg:job_name}'
                      patterns:
                        job_name_rg: '(py)?spark-(java-)?job-\d+-\w+'
                  - copy_fields:
                      fields:
                        - from: grok.job_name
                          to: job_name
                      fail_on_error: false
                      ignore_missing: true
                  - drop_fields:
                      fields: ["grok"]
                      ignore_missing: true

                  - if:
                      contains:
                        pod_name: -driver
                    then:
                      - add_fields:
                          target: ""
                          fields:
                            pod_type: driver
                    else:
                      - add_fields:
                          target: ""
                          fields:
                            pod_type: executor

      - copy_fields:
          fields:
            - from: log.offset
              to: offset
            - from: log.file.path
              to: __path__
            - from: host.name
              to: __source__
      - drop_fields:
          fields: ["agent", "log", "ecs", "tmp", "input", "host"]
          ignore_missing: true

    output.klog:
      endpoint: {{ index .Values.klogEndpoint }}
      credential:
        path: credential.ini
        check_interval: 60
      targets:
        - project_name: {{ .Values.projectName }}
          pool_name: {{ .Values.poolName }}
          fields:

    logging.to_files: true
    logging.level: info
    logging.files:
      keepfiles: 7
      rotateeverybytes: 10485760
      path: /usr/share/filebeat/logs
      permissions: 0644
