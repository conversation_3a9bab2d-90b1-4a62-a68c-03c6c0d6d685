fullName: spark-history
rbac:
  roleName: spark-role
  roleBindingName: spark
replicaCount: 1
image:
  repository: hub.kce.ksyun.com/bigdata-platform/pyspark
  tag: v3.4.3-ksc1.3
  pullPolicy: Always
ks3:
  endpoint: ks3-cn-beijing-internal.ksyuncs.com
  AccessKey: AKLT5k45XEaBSJ-Nv3o0QG0Q5A
  AccessSecret: OHRmsdngl4T7uICvcxJixTSayk7YOtGvFhofh+feeIRM9XTe07kLP8x/YIDUEproTg==
  logDirectory: ks3://zhangbo/spark/helm-test
service:
  name: http
  externalPort: 80
  internalPort: 18080
  type: ClusterIP
ingress:
  host: 2a9964c5-sparkhistory.kmr-on-kce-pre.ksyun.com
  ingressClass: kmr-on-kce
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/whitelist-source-range: "0.0.0.0/0"
resources: {}
nodeSelector: {}
tolerations: []
