apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.fullName }}-deployment
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 1
  selector:
    matchLabels:
      app: {{ .Values.fullName }}
  template:
    metadata:
      labels:
        app: {{ .Values.fullName }}
    spec:
      serviceAccountName: spark-operator-spark
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - '/opt/spark/sbin/start-history-server.sh'
          env:
            - name: SPARK_NO_DAEMONIZE
              value: "false"
            - name: SPARK_CONF_DIR
              value: /opt/spark/conf
          volumeMounts:
            - name: config-volume
              mountPath: /opt/spark/conf/
          ports:
            - name: http
              containerPort: {{ .Values.service.internalPort }}
              protocol: TCP
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        {{- with .Values.resources }}
          resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      volumes:
        - name: config-volume
          configMap:
            name: {{ .Values.fullName }}-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}