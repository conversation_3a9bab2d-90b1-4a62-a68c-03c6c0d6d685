kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ .Values.fullName }}-config
data:
  log4j2.properties: |-
    log4j.logger.com.ksyun.ks3=WARN,stdout
    log4j.logger.org.apache.http=WARN,stdout
    log4j.logger.org.apache.http.wire=WARN,stdout
    log4j.additivity.org.apache=true
    log4j.appender.stdout=org.apache.log4j.ConsoleAppender
    log4j.appender.stdout.Target=System.out
    log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
    log4j.appender.stdout.layout.ConversionPattern=[%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}] [%t] %-5level %logger{36} - %msg%n
  spark-defaults.conf: |-
    spark.history.fs.cleaner.enabled=true
    spark.history.fs.cleaner.maxAge=30d
    spark.history.fs.eventLog.rolling.maxFilesToRetain=100
    spark.hadoop.fs.ks3.endpoint={{ .Values.ks3.endpoint}}
    spark.hadoop.fs.ks3.AccessKey={{ .Values.ks3.AccessKey}}
    spark.hadoop.fs.ks3.AccessSecret={{ .Values.ks3.AccessSecret}}
    spark.history.fs.logDirectory={{ .Values.ks3.logDirectory}}
