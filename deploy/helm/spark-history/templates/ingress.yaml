apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.fullName }}-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: {{ .Values.ingress.ingressClass }}
  rules:
    - host: {{ .Values.ingress.host }}
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ .Values.fullName }}-service
                port:
                  number: {{ .Values.service.externalPort }}
