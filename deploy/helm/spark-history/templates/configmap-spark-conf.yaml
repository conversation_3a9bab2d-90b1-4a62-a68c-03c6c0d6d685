kind: ConfigMap
apiVersion: v1
metadata:
  name: serverless-spark-cm
data:
  log4j2.xml: |-
    <Configuration status="info" monitorInterval="30">
      <Properties>
          <Property name="myPattern" value="[%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}] [%t] %-5level %logger{36} - %msg%n"/>
      </Properties>
      <Appenders>
          <Console name="ConsoleAppend" target="SYSTEM_OUT">
              <PatternLayout pattern="${myPattern}"/>
          </Console>
          <File name="FileAppend" fileName="/tmp/spark/application.log">
              <PatternLayout pattern="${myPattern}"/>
          </File>
      </Appenders>
      <Loggers>
          <Root level="INFO">
              <AppenderRef ref="FileAppend"/>
          </Root>
          <Logger name="com.ksyun.ks3.http.Ks3CoreController" level="error" additivity="false">
              <AppenderRef ref="FileAppend"/>
          </Logger>
          <Logger name="org.sparkproject.jetty" level="error" additivity="false">
              <AppenderRef ref="FileAppend"/>
          </Logger>
          <Logger name="com.ksyun.kmr.hadoop.fs.ks3" level="WARN" additivity="false">
              <AppenderRef ref="FileAppend"/>
          </Logger>
      </Loggers>
    </Configuration>
