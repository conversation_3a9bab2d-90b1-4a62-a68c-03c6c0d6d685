replicaCount: 1

image:
  repository: nimbus
  tag: latest
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  className: nginx
  annotations: {}
  hosts:
    - host: nimbus.example.com
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 1
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

server:
  alarm_addr: "http://alarm.inner.sdns.ksyun.com"

database:
  host: ***********
  port: 8091
  user: admin
  password: CBd12399
  dbname: nimbus_v1

logLevel: info

kubernetes:
  defaultClusterID: 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a