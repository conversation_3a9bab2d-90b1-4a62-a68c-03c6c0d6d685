apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "nimbus.fullname" . }}-config
  labels:
    {{- include "nimbus.labels" . | nindent 4 }}
data:
  config.yaml: |
    server:
      port: {{ .Values.service.targetPort }}
    
    database:
      driver: "mysql"
      host: "{{ .Values.database.host }}"
      port: {{ .Values.database.port }}
      user: "{{ .Values.database.user }}"
      password: "{{ .Values.database.password }}"
      dbname: "{{ .Values.database.dbname }}"
    
    log_level: "{{ .Values.logLevel }}"
    
    kubernetes:
      default_cluster_id: "{{ .Values.kubernetes.defaultClusterID }}" 