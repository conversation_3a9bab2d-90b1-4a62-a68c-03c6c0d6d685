# Default values for flink-operator
# This is a YAML-formatted file.

# -- Common labels to add to the resources
commonLabels: {}

# replicaCount -- Desired number of pods
replicaCount: 1

image:
  # -- Image repository
  repository: ghcr.io/apache/flink-kubernetes-operator
  # -- Image pull policy
  pullPolicy: Always
  # -- Image tag
  tag: "1.6.1"

# -- Image pull secrets
imagePullSecrets: []

# -- String to partially override `flink-operator.fullname` template
nameOverride: ""

# -- String to override release name
fullnameOverride: ""

rbac:
  # -- Create and use RBAC resources
  create: true
  # -- Optional annotations for rbac
  annotations: {}

serviceAccount:
  # -- Create a service account for the operator
  create: true
  # -- Optional name for the operator service account
  name: ""
  # -- Optional annotations for the operator service account
  annotations: {}

# -- Pod environment variables
env:
  - name: WATCH_NAMESPACE
    value: ""
  - name: POD_NAME
    valueFrom:
      fieldRef:
        fieldPath: metadata.name
  - name: OPERATOR_NAME
    value: "flink-kubernetes-operator"

# -- Pod security context
podSecurityContext: {}

# -- Container security context
securityContext: {}

# -- Sidecar containers
sidecars: []

# -- Volumes
volumes: []

# -- Volume mounts
volumeMounts: []

# -- Metrics configuration
metrics:
  # -- Enable prometheus metrics
  enabled: true
  # -- Metrics port
  port: 9999
  # -- Metrics port name
  portName: metrics
  # -- Metrics serving endpoint
  endpoint: /metrics

# -- Webhook configuration
webhook:
  # -- Enable webhook server
  enabled: true
  # -- Webhook port
  port: 9443
  # -- Webhook port name
  portName: webhook

# -- Node selector for pod assignment
nodeSelector: {}

# -- Tolerations for pod assignment
tolerations: []

# -- Affinity for pod assignment
affinity: {}

# -- Additional annotations to add to the pod
podAnnotations: {}

# -- Additional labels to add to the pod
podLabels: {}

# -- Pod resource requests and limits
resources:
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: "100m"
    memory: 512Mi

# -- Priority class name
priorityClassName: ""

# -- Service configuration
service:
  # -- Service type
  type: ClusterIP
  # -- Service port
  port: 8080
  # -- Service target port
  targetPort: 8080
