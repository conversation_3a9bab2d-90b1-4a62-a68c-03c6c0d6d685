{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "flink-operator.fullname" . }}
  labels:
    {{- include "flink-operator.labels" . | nindent 4 }}
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.rbac.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "serviceaccounts"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "deployments/finalizers", "replicasets"]
  verbs: ["*"]
- apiGroups: ["flink.apache.org"]
  resources: ["flinkdeployments", "flinkdeployments/status", "flinksessionjobs", "flinksessionjobs/status"]
  verbs: ["*"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["*"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "flink-operator.fullname" . }}
  labels:
    {{- include "flink-operator.labels" . | nindent 4 }}
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "flink-operator.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "flink-operator.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
