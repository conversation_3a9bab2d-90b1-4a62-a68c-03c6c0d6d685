apiVersion: v1
kind: Service
metadata:
  name: {{ include "flink-operator.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "flink-operator.labels" . | nindent 4 }}
    {{- with .Values.commonLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
    {{- if .Values.metrics.enabled }}
    - port: {{ .Values.metrics.port }}
      targetPort: {{ .Values.metrics.port }}
      protocol: TCP
      name: {{ .Values.metrics.portName }}
    {{- end }}
    {{- if .Values.webhook.enabled }}
    - port: {{ .Values.webhook.port }}
      targetPort: {{ .Values.webhook.port }}
      protocol: TCP
      name: {{ .Values.webhook.portName }}
    {{- end }}
  selector:
    {{- include "flink-operator.selectorLabels" . | nindent 4 }}
