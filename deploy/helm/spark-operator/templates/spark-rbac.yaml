{{- if or .Values.rbac.create .Values.rbac.createRole }}
{{- $jobNamespaces := .Values.sparkJobNamespaces | default list }}
{{- range $jobNamespace := $jobNamespaces }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: spark-role
  namespace: {{ $jobNamespace }}
  labels:
    {{- include "spark-operator.labels" $ | nindent 4 }}
rules:
  - apiGroups: [""]
    resources: ["pods", "services", "configmaps", "persistentvolumeclaims"]
    verbs: ["*"]
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: spark
  namespace: {{ $jobNamespace }}
  labels:
    {{- include "spark-operator.labels" $ | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ include "spark.serviceAccountName" $ }}
  namespace: {{ $jobNamespace }}
roleRef:
  kind: Role
  name: spark-role
  apiGroup: rbac.authorization.k8s.io
{{- end }}
{{- end }}
