{{- if $.Values.podDisruptionBudget.enable }}
{{- if (gt (int $.Values.replicaCount) 1) }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "spark-operator.fullname" . }}-pdb
  labels:
    {{- include "spark-operator.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "spark-operator.selectorLabels" . | nindent 6 }}
  minAvailable: {{ $.Values.podDisruptionBudget.minAvailable }}
{{- else }}
{{- fail "replicaCount must be greater than 1 to enable PodDisruptionBudget" }}
{{- end }}
{{- end }}
