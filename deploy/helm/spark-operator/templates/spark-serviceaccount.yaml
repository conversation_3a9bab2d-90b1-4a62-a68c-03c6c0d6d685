{{- if .Values.serviceAccounts.spark.create }}
{{- range $sparkJobNamespace := .Values.sparkJobNamespaces | default (list .Release.Namespace) }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "spark.serviceAccountName" $ }}
  namespace: {{ $sparkJobNamespace }}
  {{- with $.Values.serviceAccounts.spark.annotations }}
  annotations: {{ toYaml . | nindent 4 }}
  {{- end }}
  labels: {{ include "spark-operator.labels" $ | nindent 4 }}
automountServiceAccountToken: true
{{- end }}
{{- end }}
