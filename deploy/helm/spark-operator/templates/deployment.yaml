# If the admission webhook is enabled, then a post-install step is required
# to generate and install the secret in the operator namespace.

# In the post-install hook, the token corresponding to the operator service account
# is used to authenticate with the Kubernetes API server to install the secret bundle.
{{- $jobNamespaces := .Values.sparkJobNamespaces | default list }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "spark-operator.fullname" . }}
  labels:
    {{- include "spark-operator.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "spark-operator.selectorLabels" . | nindent 6 }}
  strategy:
    type: Recreate
  template:
    metadata:
    {{- if or .Values.podAnnotations .Values.metrics.enable }}
      annotations:
      {{- if .Values.metrics.enable }}
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.metrics.port }}"
        prometheus.io/path: {{ .Values.metrics.endpoint }}
      {{- end }}
      {{- if .Values.podAnnotations }}
        {{- toYaml .Values.podAnnotations | trim | nindent 8 }}
      {{- end }}
    {{- end }}
      labels:
        {{- include "spark-operator.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
          {{- toYaml . | trim | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "spark-operator.serviceAccountName" . }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
      - name: {{ .Chart.Name }}
        image: {{ .Values.image.repository }}:{{ default .Chart.AppVersion .Values.image.tag }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if gt (int .Values.replicaCount) 1 }}
        env:
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
        {{- end }}
        envFrom:
          {{- toYaml .Values.envFrom | nindent 10 }}
        securityContext:
          {{- toYaml .Values.securityContext | nindent 10 }}
        {{- if or .Values.metrics.enable .Values.webhook.enable }}
        ports:
          {{ if .Values.metrics.enable -}}
          - name: {{ .Values.metrics.portName | quote }}
            containerPort: {{ .Values.metrics.port }}
          {{- end }}
          {{ if .Values.webhook.enable -}}
          - name: {{ .Values.webhook.portName | quote }}
            containerPort: {{ .Values.webhook.port }}
          {{- end }}
        {{ end -}}
        args:
        - -v={{ .Values.logLevel }}
        - -logtostderr
        {{- if eq (len $jobNamespaces) 1 }}
        - -namespace={{ index $jobNamespaces 0 }}
        {{- end }}
        - -enable-ui-service={{ .Values.uiService.enable}}
        - -ingress-url-format={{ .Values.ingressUrlFormat }}
        - -ingress-class-name={{ .Values.ingressClassName }}
        - -controller-threads={{ .Values.controllerThreads }}
        - -resync-interval={{ .Values.resyncInterval }}
        - -enable-batch-scheduler={{ .Values.batchScheduler.enable }}
        - -label-selector-filter={{ .Values.labelSelectorFilter }}
        {{- if .Values.metrics.enable }}
        - -enable-metrics=true
        - -metrics-labels=app_type
        - -metrics-port={{ .Values.metrics.port }}
        - -metrics-endpoint={{ .Values.metrics.endpoint }}
        - -metrics-prefix={{ .Values.metrics.prefix }}
        {{- end }}
        {{- if .Values.webhook.enable }}
        - -enable-webhook=true
        - -webhook-secret-name={{ include "spark-operator.webhookSecretName" . }}
        - -webhook-secret-namespace={{ .Release.Namespace }}
        - -webhook-svc-name={{ include "spark-operator.webhookServiceName" . }}
        - -webhook-svc-namespace={{ .Release.Namespace }}
        - -webhook-config-name={{ include "spark-operator.fullname" . }}-webhook-config
        - -webhook-port={{ .Values.webhook.port }}
        - -webhook-timeout={{ .Values.webhook.timeout }}
        - -webhook-namespace-selector={{ .Values.webhook.namespaceSelector }}
        - -webhook-object-selector={{ .Values.webhook.objectSelector }}
        {{- end }}
        - -enable-resource-quota-enforcement={{ .Values.resourceQuotaEnforcement.enable }}
        {{- if gt (int .Values.replicaCount) 1 }}
        - -leader-election=true
        - -leader-election-lock-namespace={{ default .Release.Namespace .Values.leaderElection.lockNamespace }}
        - -leader-election-lock-name={{ .Values.leaderElection.lockName }}
        {{- end }}
        {{- with .Values.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.volumeMounts }}
        volumeMounts:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- with .Values.sidecars }}
        {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
