apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vmagent
  labels:
    app: vmagent
spec:
  replicas: {{ .Values.Vmagent.replicas }}
  serviceName: vmagent
  selector:
    matchLabels:
      app: vmagent
  template:
    metadata:
      labels:
        app: vmagent
      annotations:
        prometheus.io/scrape: "true"
    spec:
      serviceAccountName: {{ .Release.Name }}
      containers:
        - name: agent
          resources:
            requests:
              cpu: 50m
              memory: 500Mi
            limits:
              cpu: "1"
              memory: 6Gi
          image: "{{ .Values.VMImage.imageRepositoryHost }}/{{ .Values.VMImage.repository }}:{{ .Values.VMImage.tag }}"
          imagePullPolicy: IfNotPresent
          args:
            - -promscrape.config=/config/scrape.yml
            - -promscrape.maxScrapeSize=*********
            - -remoteWrite.tmpDataPath=/tmpData
            - -promscrape.cluster.membersCount={{ .Values.Vmagent.replicas }}
            - -promscrape.cluster.replicationFactor=2 # 可以配置副本数
            - -promscrape.cluster.memberNum=$(POD_NAME)
            - -remoteWrite.url={{ .Values.Vmagent.remoteAddr }}
            - -remoteWrite.bearerToken={{.Values.Vmagent.remoteToken }}
            - -envflag.enable=true
            - -envflag.prefix=VM_
            - -loggerFormat=json
            - -remoteWrite.showURL=true
            - -promscrape.streamParse=true
            - -promscrape.maxDroppedTargets=1000000
          ports:
            - name: http
              containerPort: {{ .Values.Vmagent.httpPort }}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          volumeMounts:
            - mountPath: /vmagent
              name: cache-volume
            - mountPath: /config
              name: config
            - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              name: kube-api-access
              readOnly: true
      volumes:
        - name: cache-volume
          emptyDir: { }
        - name: config
          configMap:
            name: {{ .Release.Name }}-config
        - name: kube-api-access
          projected:
            defaultMode: 420
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  items:
                    - key: ca.crt
                      path: ca.crt
                  name: kube-root-ca.crt
              - downwardAPI:
                  items:
                    - fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.name
                      path: namespace
