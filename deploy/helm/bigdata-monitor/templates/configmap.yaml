apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-config

data:
  scrape.yml: |
    global:
      scrape_interval: 30s
      scrape_timeout: 30s
      external_labels:
        kenv:  {{ .Values.Vmagent.env }}

    scrape_configs:
      - job_name: kube-nodes
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [ __address__ ]
            regex: "(.*):10250"
            replacement: "${1}:9101"
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_node_name ]
            action: replace
            target_label: node
      - job_name: 'kube-state-metrics'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_name ]
            action: keep
            regex: kmr-kube-state-metrics
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_name ]
            action: replace
            target_label: service
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: instance
            action: replace
      - job_name: 'kube-cadvisor'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [ __address__ ]
            regex: '(.*):10250'
            replacement: '${1}:10255'
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
            replacement: $1
          - replacement: /metrics/cadvisor
            target_label: __metrics_path__
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_node_name ]
            action: replace
            target_label: node
      - job_name: 'kubelet'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [ __address__ ]
            regex: '(.*):10250'
            replacement: '${1}:10255'
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_node_name ]
            action: replace
            target_label: node
      - job_name: 'kube-scheduler'
        kubernetes_sd_configs:
          - role: pod
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_component, __meta_kubernetes_pod_labelpresent_component ]
            regex: (kube-scheduler);true
            action: keep
          - source_labels: [ __address__ ]
            regex: '(.+)'
            replacement: '${1}:10259'
            target_label: __address__
            action: replace
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: instance
            action: replace
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
      - job_name: 'kube-controller-manager'
        kubernetes_sd_configs:
          - role: pod
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_component, __meta_kubernetes_pod_labelpresent_component ]
            regex: (kube-controller-manager);true
            action: keep
          - source_labels: [ __address__ ]
            regex: '(.+)'
            replacement: '${1}:10257'
            target_label: __address__
            action: replace
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: instance
            action: replace
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
      - job_name: "spark-app"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_scrape" ]
            action: keep
            regex: true
          - source_labels: [ "__meta_kubernetes_pod_label_spark_app_name" ]
            action: keep
            regex: (.+)
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_path" ]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [ "__meta_kubernetes_namespace" ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ "__meta_kubernetes_pod_name" ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
      - job_name: "spark-operator"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_scrape" ]
            action: keep
            regex: true
          - source_labels: [ "__meta_kubernetes_namespace" ]
            action: keep
            regex: bigdata-platform
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_path" ]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [ "__meta_kubernetes_namespace" ]
            action: replace
            target_label: namespace
          - source_labels: [ "__meta_kubernetes_pod_name" ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
      - job_name: 'kube-ray'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_is_ray_node ]
            action: keep
            regex: 'yes'
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: keep
            regex: head|worker
          - source_labels: [ __meta_kubernetes_pod_container_port_name ]
            action: keep
            regex: metrics
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: replace
            target_label: ray_node_type
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: ip
            action: replace
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
          - source_labels: [ __meta_kubernetes_pod_name ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_cluster ]
            action: replace
            target_label: ray_cluster
          - source_labels: [ __meta_kubernetes_pod_label_ray_job_name ]
            action: replace
            target_label: ray_job_id
      - job_name: 'kube-ray-autoscaler'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_is_ray_node ]
            action: keep
            regex: 'yes'
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: keep
            regex: head
          - source_labels: [ __meta_kubernetes_pod_container_port_name ]
            action: keep
            regex: metrics
          - source_labels: [ __address__ ]
            regex: '(.*):8080'
            replacement: '${1}:44217'
            target_label: __address__
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: replace
            target_label: ray_node_type
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: ip
            action: replace
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
          - source_labels: [ __meta_kubernetes_pod_name ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_cluster ]
            action: replace
            target_label: ray_cluster
          - source_labels: [ __meta_kubernetes_pod_label_ray_job_name ]
            action: replace
            target_label: ray_job_id
      - job_name: 'kube-ray-dashboard'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_is_ray_node ]
            action: keep
            regex: 'yes'
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: keep
            regex: head
          - source_labels: [ __meta_kubernetes_pod_container_port_name ]
            action: keep
            regex: metrics
          - source_labels: [ __address__ ]
            regex: '(.*):8080'
            replacement: '${1}:44227'
            target_label: __address__
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_node_type ]
            action: replace
            target_label: ray_node_type
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata-platform|bigdata
            action: keep
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: ip
            action: replace
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node
          - source_labels: [ __meta_kubernetes_pod_name ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_label_ray_io_cluster ]
            action: replace
            target_label: ray_cluster
          - source_labels: [ __meta_kubernetes_pod_label_ray_job_name ]
            action: replace
            target_label: ray_job_id
      - job_name: "juicefs-sync"
        scrape_interval: 5s
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_scrape" ]
            action: keep
            regex: true
          - source_labels: [ "__meta_kubernetes_pod_label_role" ]
            action: keep
            regex: juicefs-sync-job-manager
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_path" ]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: [ "__meta_kubernetes_pod_annotation_prometheus_io_account_id" ]
            target_label: vm_account_id
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [ "__meta_kubernetes_namespace" ]
            action: replace
            target_label: namespace
          - source_labels: [ __meta_kubernetes_namespace ]
            regex: bigdata
            action: keep
          - source_labels: [ "__meta_kubernetes_pod_name" ]
            action: replace
            target_label: pod
          - source_labels: [ __meta_kubernetes_pod_node_name ]
            action: replace
            target_label: node