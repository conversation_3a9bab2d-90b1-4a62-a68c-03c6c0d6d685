{{- if and .Values.server.enabled .Values.server.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "argo-workflows.serverServiceAccountName" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
  {{- with .Values.server.serviceAccount.labels  }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.server.serviceAccount.annotations  }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end -}}
