{{- if and .Values.server.enabled .Values.server.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
{{- if .Values.singleNamespace }}
kind: RoleBinding
{{- else }}
kind: ClusterRoleBinding
{{- end }}
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}
  {{- if .Values.singleNamespace }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  {{- end }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  {{- if .Values.singleNamespace }}
  kind: Role
  {{- else }}
  kind: ClusterRole
  {{- end }}
  name: {{ template "argo-workflows.server.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "argo-workflows.serverServiceAccountName" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}

{{- if .Values.server.clusterWorkflowTemplates.enabled }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}-cluster-template
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "argo-workflows.server.fullname" . }}-cluster-template
subjects:
- kind: ServiceAccount
  name: {{ template "argo-workflows.serverServiceAccountName" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
{{- end -}}
{{- end -}}
