{{- if and .Values.server.enabled .Values.server.rbac.create}}
apiVersion: rbac.authorization.k8s.io/v1
  {{- if .Values.singleNamespace }}
kind: Role
  {{- else }}
kind: ClusterRole
  {{- end }}
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}
  {{- if .Values.singleNamespace }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  {{- end }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - events
  verbs:
  - get
  - watch
  - list
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
  - delete
- apiGroups:
  - ""
  resources:
  - pods/log
  verbs:
  - get
  - list
{{- if .Values.server.sso.enabled }}
- apiGroups:
  - ""
  resources:
  - secrets
  resourceNames:
  - sso
  verbs:
  - get
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  {{- if .Values.server.sso.rbac.enabled }}
- apiGroups:
  - ""
  resources:
  - serviceaccounts
  verbs:
  - get
  - list
  - watch
  {{- end }}
{{- end }}
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
{{- if and .Values.server.sso.enabled .Values.server.sso.rbac.enabled }}
  {{- with .Values.server.sso.rbac.secretWhitelist }}
  resourceNames: {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - watch
  - create
  - patch
{{- if .Values.controller.persistence }}
- apiGroups:
  - ""
  resources:
  - secrets
  resourceNames:
  {{- with .Values.controller.persistence.postgresql }}
  - {{ .userNameSecret.name }}
  - {{ .passwordSecret.name }}
  {{- end}}
  {{- with .Values.controller.persistence.mysql }}
  - {{ .userNameSecret.name }}
  - {{ .passwordSecret.name }}
  {{- end}}
  verbs:
  - get
{{- end}}
- apiGroups:
    - argoproj.io
  resources:
    - eventsources
    - sensors
    - workflows
    - workfloweventbindings
    - workflowtemplates
    - cronworkflows
  verbs:
    - create
    - get
    - list
    - watch
    - update
    - patch
    - delete

{{- if and .Values.server.clusterWorkflowTemplates.enabled (not .Values.singleNamespace) }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}-cluster-template
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - clusterworkflowtemplates
  verbs:
  - get
  - list
  - watch
  {{- if .Values.server.clusterWorkflowTemplates.enableEditing }}
  - create
  - update
  - patch
  - delete
  {{- end }}
{{- end }}
{{- end }}
