{{- if .Values.server.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    app.kubernetes.io/version: {{ include "argo-workflows.server_chart_version_label" . }}
    {{- with .Values.server.serviceLabels }}
      {{ toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.serviceAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ports:
  - port: {{ .Values.server.servicePort }}
    {{- with .Values.server.servicePortName }}
    name: {{ . }}
    {{- end }}
    targetPort: 2746
    {{- if and (eq .Values.server.serviceType "NodePort") .Values.server.serviceNodePort }}
    nodePort: {{ .Values.server.serviceNodePort }}
    {{- end }}
  selector:
    {{- include "argo-workflows.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 4 }}
  sessionAffinity: None
  type: {{ .Values.server.serviceType }}
  {{- if eq .Values.server.serviceType "LoadBalancer" }}
  {{- with .Values.controller.loadBalancerClass }}
  loadBalancerClass: {{ . }}
  {{- end }}
  {{- with .Values.server.loadBalancerIP }}
  loadBalancerIP: {{ . | quote }}
  {{- end }}
  {{- if .Values.server.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
    {{- toYaml .Values.server.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  {{- end }}
{{- end -}}
