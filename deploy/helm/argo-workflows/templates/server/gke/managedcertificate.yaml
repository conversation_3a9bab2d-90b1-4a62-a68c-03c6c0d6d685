{{- if and .Values.server.enabled .Values.server.GKEmanagedCertificate.enabled }}
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
spec:
  domains:
    {{- with .Values.server.GKEmanagedCertificate.domains }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
