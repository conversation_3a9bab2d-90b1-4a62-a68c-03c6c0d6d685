{{- if .Values.server.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "argo-workflows.server.fullname" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    app.kubernetes.io/version: {{ include "argo-workflows.server_chart_version_label" . }}
  {{- with .Values.server.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.server.autoscaling.enabled }}
  replicas: {{ .Values.server.replicas }}
  {{- end }}
  revisionHistoryLimit: {{ .Values.server.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-workflows.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "argo-workflows.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 8 }}
        app.kubernetes.io/version: {{ include "argo-workflows.server_chart_version_label" . }}
        {{- with .Values.server.podLabels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- if or  .Values.server.podAnnotations .Values.controller.configMap.create }}
      annotations:
        {{- with .Values.server.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- if .Values.controller.configMap.create }}
        checksum/cm: {{ include (print $.Template.BasePath "/controller/workflow-controller-config-map.yaml") . | sha256sum }}
        {{- end }}
      {{- end }}
    spec:
      serviceAccountName: {{ template "argo-workflows.serverServiceAccountName" . }}
      {{- with .Values.server.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.extraInitContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      containers:
        - name: argo-server
          image: "{{- include "argo-workflows.image" (dict "context" . "image" .Values.server.image) }}:{{ default (include "argo-workflows.defaultTag" .) .Values.server.image.tag }}"
          imagePullPolicy: {{ .Values.images.pullPolicy }}
          securityContext:
            {{- toYaml .Values.server.securityContext | nindent 12 }}
          args:
          - server
          - --configmap={{ template "argo-workflows.controller.config-map.name" . }}
          {{- with .Values.server.extraArgs }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- if .Values.server.authMode }}
          - "--auth-mode={{ .Values.server.authMode }}"
          {{- end }}
          {{- range .Values.server.authModes }}
          - "--auth-mode={{ . }}"
          {{- end }}
          - "--secure={{ .Values.server.secure }}"
          {{- if .Values.singleNamespace }}
          - "--namespaced"
          {{- end }}
          - "--loglevel"
          - "{{ .Values.server.logging.level }}"
          - "--gloglevel"
          - "{{ .Values.server.logging.globallevel }}"
          - "--log-format"
          - "{{ .Values.server.logging.format }}"
          ports:
          - name: web
            containerPort: 2746
          readinessProbe:
            httpGet:
              path: /
              port: 2746
              {{- if .Values.server.secure }}
              scheme: HTTPS
              {{- else }}
              scheme: HTTP
              {{- end }}
            initialDelaySeconds: 10
            periodSeconds: 20
          {{- if .Values.server.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.server.livenessProbe.httpGet.path }}
              port: {{ .Values.server.livenessProbe.httpGet.port }}
              {{- if .Values.server.secure }}
              scheme: HTTPS
              {{- else }}
              scheme: HTTP
              {{- end }}
            initialDelaySeconds: {{ .Values.server.livenessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.server.livenessProbe.timeoutSeconds }}
            periodSeconds: {{ .Values.server.livenessProbe.periodSeconds }}
            successThreshold: {{ .Values.server.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.server.livenessProbe.failureThreshold }}
          {{- end }}
          env:
            - name: IN_CLUSTER
              value: "true"
            - name: ARGO_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: ARGO_BASE_HREF
              value: {{ .Values.server.baseHref | quote }}
          {{- with .Values.server.extraEnv }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.server.resources | nindent 12 }}
          volumeMounts:
          - name: tmp
            mountPath: /tmp
          {{- with .Values.server.volumeMounts }}
            {{- toYaml . | nindent 10}}
          {{- end }}
          {{- with .Values.server.lifecycle }}
          lifecycle:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- with .Values.server.extraContainers }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.server.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ . }}
      {{- end }}
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: tmp
        {{- toYaml .Values.server.tmpVolume | nindent 8 }}
      {{- with .Values.server.volumes }}
        {{- toYaml . | nindent 6}}
      {{- end }}
      {{- with .Values.server.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-workflows.selectorLabels" (dict "context" $ "name" $.Values.server.name) | nindent 12 }}
        {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.server.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
{{- end -}}
