{{- if .Values.controller.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
spec:
  {{- if .Values.controller.pdb.minAvailable }}
  minAvailable: {{ .Values.controller.pdb.minAvailable }}
  {{- else if .Values.controller.pdb.maxUnavailable }}
  maxUnavailable: {{ .Values.controller.pdb.maxUnavailable }}
  {{- else }}
  minAvailable: 0
  {{- end }}
  selector:
    matchLabels:
      {{- include "argo-workflows.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 6 }}
{{- end }}
