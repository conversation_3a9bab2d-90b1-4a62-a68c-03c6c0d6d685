apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
    app.kubernetes.io/version: {{ include "argo-workflows.controller_chart_version_label" . }}
  {{- with .Values.controller.deploymentAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.controller.replicas }}
  revisionHistoryLimit: {{ .Values.controller.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-workflows.selectorLabels" (dict "context" . "name" .Values.controller.name) | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 8 }}
        app.kubernetes.io/version: {{ include "argo-workflows.controller_chart_version_label" . }}
        {{- with.Values.controller.podLabels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.controller.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      serviceAccountName: {{ template "argo-workflows.controllerServiceAccountName" . }}
      {{- with .Values.controller.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.extraInitContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
      containers:
        - name: controller
          image: "{{- include "argo-workflows.image" (dict "context" . "image" .Values.controller.image) }}:{{ default (include "argo-workflows.defaultTag" .) .Values.controller.image.tag }}"
          imagePullPolicy: {{ .Values.images.pullPolicy }}
          command: [ "workflow-controller" ]
          args:
          - "--configmap"
          - "{{ template "argo-workflows.controller.config-map.name" . }}"
          - "--executor-image"
          - "{{- include "argo-workflows.image" (dict "context" . "image" .Values.executor.image) }}:{{ default (include "argo-workflows.defaultTag" .) .Values.executor.image.tag }}"
          - "--loglevel"
          - "{{ .Values.controller.logging.level }}"
          - "--gloglevel"
          - "{{ .Values.controller.logging.globallevel }}"
          - "--log-format"
          - "{{ .Values.controller.logging.format }}"
          {{- if .Values.singleNamespace }}
          - "--namespaced"
          {{- end }}
          {{- with .Values.controller.workflowWorkers }}
          - "--workflow-workers"
          - {{ . | quote }}
          {{- end }}
          {{- with .Values.controller.workflowTTLWorkers }}
          - "--workflow-ttl-workers"
          - {{ . | quote }}
          {{- end }}
          {{- with .Values.controller.podCleanupWorkers }}
          - "--pod-cleanup-workers"
          - {{ . | quote }}
          {{- end }}
          {{- with .Values.controller.cronWorkflowWorkers }}
          - "--cron-workflow-workers"
          - {{ . | quote }}
          {{- end }}
          {{- with .Values.controller.extraArgs }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          securityContext:
            {{- toYaml .Values.controller.securityContext | nindent 12 }}
          env:
            - name: ARGO_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: LEADER_ELECTION_IDENTITY
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          {{- if eq (int .Values.controller.replicas) 1 }}
            - name: LEADER_ELECTION_DISABLE
              value: "true"
          {{- end }}
          {{- with .Values.controller.extraEnv }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.controller.resources | nindent 12 }}
          {{- with .Values.controller.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 10 }}
          {{- end }}
          ports:
            - name: {{ .Values.controller.metricsConfig.portName }}
              containerPort: {{ .Values.controller.metricsConfig.port }}
            - containerPort: 6060
          livenessProbe:
            {{- .Values.controller.livenessProbe | toYaml | nindent 12 }}
        {{- with .Values.controller.extraContainers }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.images.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.volumes }}
      volumes:
        {{- toYaml . | nindent 6 }}
      {{- end }}
      {{- with .Values.controller.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controller.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-workflows.selectorLabels" (dict "context" $ "name" $.Values.controller.name) | nindent 12 }}
        {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.controller.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
