{{- if .Values.controller.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "argo-workflows.controllerServiceAccountName" . }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
  {{- with .Values.controller.serviceAccount.labels  }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.controller.serviceAccount.annotations }}
  annotations:
    {{- toYaml .| nindent 4 }}
  {{- end }}
{{- end }}
