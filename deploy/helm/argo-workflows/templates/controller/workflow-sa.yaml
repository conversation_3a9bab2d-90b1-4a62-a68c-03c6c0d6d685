{{- if .Values.workflow.serviceAccount.create -}}
  {{- range $namespace := or .Values.singleNamespace false | ternary (list "") (append .Values.controller.workflowNamespaces (coalesce .Values.workflow.namespace (include "argo-workflows.namespace" .)) | uniq)  }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $.Values.workflow.serviceAccount.name }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" $ "component" $.Values.controller.name "name" $.Values.controller.name) | nindent 4 }}
  {{- with $.Values.workflow.serviceAccount.labels  }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
    {{- with $namespace }}
  namespace: {{ . }}
    {{- end }}
    {{- with $.Values.workflow.serviceAccount.annotations }}
  annotations:
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with $.Values.workflow.serviceAccount.pullSecrets }}
imagePullSecrets:
    {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- end }}
{{- end }}
