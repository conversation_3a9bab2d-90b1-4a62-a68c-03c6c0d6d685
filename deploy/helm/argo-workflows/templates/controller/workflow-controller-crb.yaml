{{- if .Values.controller.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
{{- if .Values.singleNamespace }}
kind: RoleBinding
{{- else }}
kind: ClusterRoleBinding
{{- end }}
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}
  {{- if .Values.singleNamespace }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  {{- end }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  {{- if .Values.singleNamespace }}
  kind: Role
  {{- else }}
  kind: ClusterRole
  {{- end }}
  name: {{ template "argo-workflows.controller.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "argo-workflows.controllerServiceAccountName" . }}
    namespace: {{ include "argo-workflows.namespace" . | quote }}

{{- if .Values.controller.clusterWorkflowTemplates.enabled }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}-cluster-template
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "argo-workflows.controller.fullname" . }}-cluster-template
subjects:
  - kind: ServiceAccount
    name: {{ template "argo-workflows.controllerServiceAccountName" . }}
    namespace: {{ include "argo-workflows.namespace" . | quote }}
{{- range .Values.controller.clusterWorkflowTemplates.serviceAccounts }}
  - kind: ServiceAccount
    name: {{ .name }}
    namespace: {{ .namespace | quote }}
{{- end }}
{{- end }}
{{- end }}
