{{- if .Values.controller.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
{{- if .Values.singleNamespace }}
kind: Role
{{- else }}
kind: ClusterRole
{{- end }}
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}
  {{- if .Values.singleNamespace }}
  namespace: {{ include "argo-workflows.namespace" . | quote }}
  {{- end }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - get
  - list
  - watch
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - pods/exec
  verbs:
  - create
- apiGroups:
  - ""
  resources:
  - configmaps
  - namespaces
  verbs:
  - get
  - watch
  - list
  {{- if .Values.controller.rbac.writeConfigMaps }}
  - create
  - update
  {{- end}}
- apiGroups:
  - ""
  resources:
  - persistentvolumeclaims
  - persistentvolumeclaims/finalizers
  verbs:
  - create
  - update
  - delete
  - get
- apiGroups:
  - argoproj.io
  resources:
  - workflows
  - workflows/finalizers
  - workflowtasksets
  - workflowtasksets/finalizers
  - workflowartifactgctasks
  verbs:
  - get
  - list
  - watch
  - update
  - patch
  - delete
  - create
- apiGroups:
  - argoproj.io
  resources:
  - workflowtemplates
  - workflowtemplates/finalizers
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - argoproj.io
  resources:
  - workflowtaskresults
  - workflowtaskresults/finalizers
  verbs:
  - list
  - watch
  - deletecollection
- apiGroups:
  - argoproj.io
  resources:
  - cronworkflows
  - cronworkflows/finalizers
  verbs:
  - get
  - list
  - watch
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - serviceaccounts
  verbs:
  - get
  - list
- apiGroups:
  - "policy"
  resources:
  - poddisruptionbudgets
  verbs:
  - create
  - get
  - delete
{{- if .Values.controller.persistence }}
- apiGroups:
  - ""
  resources:
  - secrets
  resourceNames:
  {{- if .Values.controller.persistence.postgresql }}
  - {{ .Values.controller.persistence.postgresql.userNameSecret.name }}
  - {{ .Values.controller.persistence.postgresql.passwordSecret.name }}
  {{- end}}
  {{- if .Values.controller.persistence.mysql }}
  - {{ .Values.controller.persistence.mysql.userNameSecret.name }}
  - {{ .Values.controller.persistence.mysql.passwordSecret.name }}
  {{- end}}
  verbs:
  - get
{{- end}}
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  resourceNames:
  {{- if .Values.controller.instanceID.enabled }}
    {{- if .Values.controller.instanceID.useReleaseName }}
  - workflow-controller-{{ .Release.Name }}
  - workflow-controller-lease-{{ .Release.Name }}
    {{- else }}
  - workflow-controller-{{ .Values.controller.instanceID.explicitID }}
  - workflow-controller-lease-{{ .Values.controller.instanceID.explicitID }}
    {{- end }}
  {{- else }}
  - workflow-controller
  - workflow-controller-lease
  {{- end }}
  verbs:
  - get
  - watch
  - update
  - patch
  - delete
{{- if .Values.controller.rbac.accessAllSecrets }}
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
{{- else }}
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  resourceNames:
  {{- /* for HTTP templates */}}
  - argo-workflows-agent-ca-certificates
{{- with .Values.controller.rbac.secretWhitelist }}
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
  resourceNames: {{- toYaml . | nindent 4 }}
{{- end }}
{{- end }}

{{- if and .Values.controller.clusterWorkflowTemplates.enabled (not .Values.singleNamespace) }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ template "argo-workflows.controller.fullname" . }}-cluster-template
  labels:
    {{- include "argo-workflows.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
rules:
- apiGroups:
  - argoproj.io
  resources:
  - clusterworkflowtemplates
  - clusterworkflowtemplates/finalizers
  verbs:
  - get
  - list
  - watch
{{- end }}
{{- end }}
