{{- if .Values.workflow.serviceAccount.create -}}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kubectl-admin-sa
  namespace: {{ include "argo-workflows.namespace" . }}
  labels:
    {{- include "argo-workflows.labels" (dict "context" $ "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
    app.kubernetes.io/component: kubectl-admin
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubectl-admin-sa-cluster-admin
  labels:
    {{- include "argo-workflows.labels" (dict "context" $ "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
    app.kubernetes.io/component: kubectl-admin
subjects:
- kind: ServiceAccount
  name: kubectl-admin-sa
  namespace: {{ include "argo-workflows.namespace" . }}
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io
{{- end }}