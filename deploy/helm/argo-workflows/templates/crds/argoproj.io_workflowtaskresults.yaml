{{- if .Values.crds.install }}
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: workflowtaskresults.argoproj.io
  annotations:
    {{- if .Values.crds.keep }}
    "helm.sh/resource-policy": keep
    {{- end }}
    {{- with .Values.crds.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  group: argoproj.io
  names:
    kind: WorkflowTaskResult
    listKind: WorkflowTaskResultList
    plural: workflowtaskresults
    singular: workflowtaskresult
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          message:
            type: string
          metadata:
            type: object
          outputs:
            properties:
              artifacts:
                items:
                  properties:
                    archive:
                      properties:
                        none:
                          type: object
                        tar:
                          properties:
                            compressionLevel:
                              format: int32
                              type: integer
                          type: object
                        zip:
                          type: object
                      type: object
                    archiveLogs:
                      type: boolean
                    artifactGC:
                      properties:
                        podMetadata:
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              type: object
                            labels:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                        serviceAccountName:
                          type: string
                        strategy:
                          enum:
                          - ""
                          - OnWorkflowCompletion
                          - OnWorkflowDeletion
                          - Never
                          type: string
                      type: object
                    artifactory:
                      properties:
                        passwordSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        url:
                          type: string
                        usernameSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - url
                      type: object
                    azure:
                      properties:
                        accountKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        blob:
                          type: string
                        container:
                          type: string
                        endpoint:
                          type: string
                        useSDKCreds:
                          type: boolean
                      required:
                      - blob
                      - container
                      - endpoint
                      type: object
                    deleted:
                      type: boolean
                    from:
                      type: string
                    fromExpression:
                      type: string
                    gcs:
                      properties:
                        bucket:
                          type: string
                        key:
                          type: string
                        serviceAccountKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - key
                      type: object
                    git:
                      properties:
                        branch:
                          type: string
                        depth:
                          format: int64
                          type: integer
                        disableSubmodules:
                          type: boolean
                        fetch:
                          items:
                            type: string
                          type: array
                        insecureIgnoreHostKey:
                          type: boolean
                        insecureSkipTLS:
                          type: boolean
                        passwordSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        repo:
                          type: string
                        revision:
                          type: string
                        singleBranch:
                          type: boolean
                        sshPrivateKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        usernameSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - repo
                      type: object
                    globalName:
                      type: string
                    hdfs:
                      properties:
                        addresses:
                          items:
                            type: string
                          type: array
                        dataTransferProtection:
                          type: string
                        force:
                          type: boolean
                        hdfsUser:
                          type: string
                        krbCCacheSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        krbConfigConfigMap:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        krbKeytabSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        krbRealm:
                          type: string
                        krbServicePrincipalName:
                          type: string
                        krbUsername:
                          type: string
                        path:
                          type: string
                      required:
                      - path
                      type: object
                    http:
                      properties:
                        auth:
                          properties:
                            basicAuth:
                              properties:
                                passwordSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                usernameSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                              type: object
                            clientCert:
                              properties:
                                clientCertSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                clientKeySecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                              type: object
                            oauth2:
                              properties:
                                clientIDSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                clientSecretSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                endpointParams:
                                  items:
                                    properties:
                                      key:
                                        type: string
                                      value:
                                        type: string
                                    required:
                                    - key
                                    type: object
                                  type: array
                                scopes:
                                  items:
                                    type: string
                                  type: array
                                tokenURLSecret:
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      default: ""
                                      type: string
                                    optional:
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                              type: object
                          type: object
                        headers:
                          items:
                            properties:
                              name:
                                type: string
                              value:
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                        url:
                          type: string
                      required:
                      - url
                      type: object
                    mode:
                      format: int32
                      type: integer
                    name:
                      type: string
                    optional:
                      type: boolean
                    oss:
                      properties:
                        accessKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        bucket:
                          type: string
                        createBucketIfNotPresent:
                          type: boolean
                        endpoint:
                          type: string
                        key:
                          type: string
                        lifecycleRule:
                          properties:
                            markDeletionAfterDays:
                              format: int32
                              type: integer
                            markInfrequentAccessAfterDays:
                              format: int32
                              type: integer
                          type: object
                        secretKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        securityToken:
                          type: string
                        useSDKCreds:
                          type: boolean
                      required:
                      - key
                      type: object
                    path:
                      type: string
                    raw:
                      properties:
                        data:
                          type: string
                      required:
                      - data
                      type: object
                    recurseMode:
                      type: boolean
                    s3:
                      properties:
                        accessKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        bucket:
                          type: string
                        caSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        createBucketIfNotPresent:
                          properties:
                            objectLocking:
                              type: boolean
                          type: object
                        encryptionOptions:
                          properties:
                            enableEncryption:
                              type: boolean
                            kmsEncryptionContext:
                              type: string
                            kmsKeyId:
                              type: string
                            serverSideCustomerKeySecret:
                              properties:
                                key:
                                  type: string
                                name:
                                  default: ""
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        endpoint:
                          type: string
                        insecure:
                          type: boolean
                        key:
                          type: string
                        region:
                          type: string
                        roleARN:
                          type: string
                        secretKeySecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        sessionTokenSecret:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        useSDKCreds:
                          type: boolean
                      type: object
                    subPath:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              exitCode:
                type: string
              parameters:
                items:
                  properties:
                    default:
                      type: string
                    description:
                      type: string
                    enum:
                      items:
                        type: string
                      type: array
                    globalName:
                      type: string
                    name:
                      type: string
                    value:
                      type: string
                    valueFrom:
                      properties:
                        configMapKeyRef:
                          properties:
                            key:
                              type: string
                            name:
                              default: ""
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        default:
                          type: string
                        event:
                          type: string
                        expression:
                          type: string
                        jqFilter:
                          type: string
                        jsonPath:
                          type: string
                        parameter:
                          type: string
                        path:
                          type: string
                        supplied:
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              result:
                type: string
            type: object
          phase:
            type: string
          progress:
            type: string
        required:
        - metadata
        type: object
    served: true
    storage: true
{{- end }}
