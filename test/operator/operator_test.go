package operator

import (
	"context"
	"os"
	"testing"

	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/internal/service"
	"github.com/kingsoft/nimbus/pkg/config"
	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/stretchr/testify/assert"
)

func TestOperator(t *testing.T) {
	// 初始化日志
	logger.InitLogger("debug")

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	err = repository.InitDB(cfg.Database)
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	// 创建Kubernetes客户端管理器
	clientManager := kubernetes.NewClientManager()

	// 创建Operator工厂
	operatorFactory := kubernetes.NewOperatorFactory(clientManager)

	// 创建仓库
	appRepo := repository.NewApplicationRepository()
	clusterRepo := repository.NewClusterRepository()
	auditLogRepo := repository.NewAuditLogRepository()

	// 创建服务
	clusterService := service.NewClusterService(clusterRepo, auditLogRepo, clientManager)
	operatorService := service.NewOperatorService(clusterRepo, auditLogRepo, operatorFactory)
	applicationService := service.NewApplicationService(appRepo, clusterRepo, auditLogRepo, operatorFactory)

	// 测试k8s集群ID（使用配置中的测试集群ID）
	testKceClusterID := cfg.Kubernetes.DefaultClusterID

	// 创建测试集群
	ctx := context.Background()
	accountID := "**********"
	testCluster, err := clusterService.Create("测试集群", "操作符安装测试", testKceClusterID, accountID, "AKLT5k45XEaBSJ-Nv3o0QG0Q5A", "OHRmsdngl4T7uICvcxJixTSayk7YOtGvFhofh+feeIRM9XTe07kLP8x/YIDUEproTg==", "ks3://zhangbo/spark/helm-test")
	if err != nil {
		t.Fatalf("创建测试集群失败: %v", err)
	}
	defer func() {
		testCluster.Status = model.ClusterStatusDeleted
		err = clusterRepo.Update(testCluster)
		if err != nil {
			t.Logf("修改集群状态失败: %v", err)
		}
		err = clusterService.Delete(testCluster.ID)
		if err != nil {
			t.Logf("清理测试集群失败: %v", err)
		}
	}()

	t.Run("验证连接", func(t *testing.T) {
		err := clusterService.ValidateConnection(ctx, testCluster.ID)
		if err != nil {
			t.Fatalf("验证集群连接失败: %v", err)
		}

		// 重新获取集群以检查状态
		cluster, err := clusterService.GetByID(testCluster.ID)
		if err != nil {
			t.Fatalf("获取集群失败: %v", err)
		}
		assert.Equal(t, model.ClusterStatusActive, cluster.Status, "集群状态应为active")
	})

	t.Run("检查SparkOperator状态", func(t *testing.T) {
		installed, err := operatorService.IsOperatorInstalled(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
		if err != nil {
			t.Fatalf("检查Spark Operator状态失败: %v", err)
		}
		if !installed {
			err := operatorService.InstallOperator(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
			if err != nil {
				t.Fatalf("安装Spark Operator失败: %v", err)
			}
		}
		t.Logf("Spark Operator已安装: %v", installed)
	})

	t.Run("卸载SparkOperator", func(t *testing.T) {
		installed, err := operatorService.IsOperatorInstalled(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
		if err != nil {
			t.Fatalf("检查Spark Operator状态失败: %v", err)
		}
		if installed {
			err := operatorService.UninstallOperator(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
			if err != nil {
				t.Fatalf("卸载Spark Operator失败: %v", err)
			}
		}
		t.Logf("Spark Operator已卸载: %v", installed)
	})

	t.Run("检查FlinkOperator状态", func(t *testing.T) {
		installed, err := operatorService.IsOperatorInstalled(ctx, testCluster.ID, kubernetes.OperatorTypeFlink)
		if err != nil {
			t.Fatalf("检查Flink Operator状态失败: %v", err)
		}
		t.Logf("Flink Operator已安装: %v", installed)
	})

	t.Run("applicationService", func(t *testing.T) {
		err := applicationService.InstallSparkOperator(ctx, testCluster.ID)
		if err != nil {
			t.Fatalf("applicationService.InstallSparkOperator: %v", err)
		}
	})

	// 根据环境变量决定是否执行安装测试（避免重复安装）
	if os.Getenv("RUN_OPERATOR_INSTALL_TEST") == "true" {
		t.Run("安装SparkOperator", func(t *testing.T) {
			err := operatorService.InstallOperator(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
			if err != nil {
				t.Fatalf("安装Spark Operator失败: %v", err)
			}

			// 验证安装结果
			installed, err := operatorService.IsOperatorInstalled(ctx, testCluster.ID, kubernetes.OperatorTypeSpark)
			if err != nil {
				t.Fatalf("检查Spark Operator状态失败: %v", err)
			}
			assert.True(t, installed, "Spark Operator应已安装")
		})

		t.Run("安装FlinkOperator", func(t *testing.T) {
			err := operatorService.InstallOperator(ctx, testCluster.ID, kubernetes.OperatorTypeFlink)
			if err != nil {
				t.Fatalf("安装Flink Operator失败: %v", err)
			}

			// 验证安装结果
			installed, err := operatorService.IsOperatorInstalled(ctx, testCluster.ID, kubernetes.OperatorTypeFlink)
			if err != nil {
				t.Fatalf("检查Flink Operator状态失败: %v", err)
			}
			assert.True(t, installed, "Flink Operator应已安装")
		})
	} else {
		t.Log("跳过安装测试，设置RUN_OPERATOR_INSTALL_TEST=true环境变量以执行安装测试")
	}
}
