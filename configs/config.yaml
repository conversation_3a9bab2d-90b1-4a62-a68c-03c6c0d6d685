server:
  port: 8080
  host: "0.0.0.0"
  alarm_addr: "http://alarm.inner.sdns.ksyun.com"

database:
  driver: "mysql"
  host: "***********"
  port: 8091
  user: "admin"
  password: "CBd12399"
  dbname: "nimbus_v1"

log_level: "info"

pod_cleanup:
  enabled: true
  retention_time: "12h"  # 保留时间，超过此时间的已完成Pod将被清理
  cleanup_interval: "60s"  # 清理检查间隔
  sync_interval: "30s"   # 状态同步间隔
  namespaces:
    - "argo"
    - "default"
    - "kmrspark"
    - "kmrflink"
  label_selector: "app=nimbus"  # 只清理带有此标签的Pod

kubernetes:
  default_cluster_id: "2a9964c5-d3b9-4b95-8b32-5e752fcbb24a"