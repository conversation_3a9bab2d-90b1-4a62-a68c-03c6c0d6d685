# Pod清理和状态监控功能

## 概述

为了解决工作流完成后Pod状态变成Completed但没有自动删除机制，以及集群和工作流状态字段没有变成完成状态的问题，我们实现了完整的Pod监控和清理系统。

## 功能特性

### 1. 自动Pod清理
- **自动检测**：系统会自动检测Completed和Failed状态的Pod
- **保留时间**：默认保留1小时，超过时间的Pod会被自动清理
- **标签过滤**：只清理带有`app=nimbus`标签的Pod
- **命名空间支持**：支持多个命名空间的Pod清理（argo, default, nimbus-system, kmrspark, kmrflink）

### 2. 工作流状态同步
- **定时同步**：每30秒自动同步所有运行中和待处理的工作流状态
- **状态映射**：将Argo Workflow状态映射到内部状态
- **集群状态更新**：工作流完成后自动更新集群状态

### 3. 集群状态管理
- **自动更新**：根据工作流类型和状态自动更新集群状态
- **状态映射**：
  - 创建集群成功：`running`
  - 创建集群失败：`failed`
  - 删除集群成功：`deleted`

## 配置选项

在`configs/config.yaml`中可以配置以下选项：

```yaml
pod_cleanup:
  enabled: true                    # 是否启用Pod清理
  retention_time: "1h"            # Pod保留时间
  cleanup_interval: "60s"         # 清理检查间隔
  sync_interval: "30s"            # 状态同步间隔
  namespaces:                      # 要监控的命名空间
    - "argo"
    - "default"
    - "kmrspark"
    - "kmrflink"
  label_selector: "app=nimbus"    # Pod标签选择器
```

## API端点

### 手动触发Pod清理
```bash
POST /api/v1/workflows/cleanup-pods?cluster_id=<cluster_id>
```

### 手动同步所有工作流状态
```bash
POST /api/v1/workflows/sync-all
```

### 启动Pod监控
```bash
POST /api/v1/workflows/monitor/start
```

### 停止Pod监控
```bash
POST /api/v1/workflows/monitor/stop
```

### 同步特定工作流状态
```bash
POST /api/v1/workflows/{workflow_id}/sync
```

## 使用示例

### 1. 启动服务
服务启动时会自动启动Pod监控：

```bash
go run cmd/nimbus/main.go
```

### 2. 手动触发清理
```bash
# 清理指定集群的Pod
curl -X POST "http://localhost:8080/api/v1/workflows/cleanup-pods?cluster_id=your-cluster-id"

# 同步所有工作流状态
curl -X POST "http://localhost:8080/api/v1/workflows/sync-all"
```

### 3. 测试脚本
使用提供的测试脚本：

```bash
./scripts/test_pod_cleanup.sh
```

## 监控日志

系统会记录以下日志：

- Pod清理操作
- 工作流状态同步
- 集群状态更新
- 错误和异常情况

## 故障排除

### 1. Pod没有被清理
- 检查Pod是否有正确的标签（`app=nimbus`）
- 检查Pod是否在配置的命名空间中
- 检查Pod是否已经超过保留时间

### 2. 工作流状态没有同步
- 检查Argo Workflow是否正常运行
- 检查网络连接是否正常
- 查看日志中的错误信息

### 3. 集群状态没有更新
- 检查工作流名称是否符合预期格式
- 检查数据库连接是否正常
- 查看日志中的错误信息

## 性能考虑

- **清理间隔**：默认60秒，可根据需要调整
- **同步间隔**：默认30秒，可根据需要调整
- **保留时间**：默认1小时，可根据需要调整
- **并发处理**：使用goroutine进行并发处理，避免阻塞主服务

## 安全考虑

- 只清理带有特定标签的Pod，避免误删
- 支持配置保留时间，确保重要Pod不被过早删除
- 记录所有操作日志，便于审计和故障排查
