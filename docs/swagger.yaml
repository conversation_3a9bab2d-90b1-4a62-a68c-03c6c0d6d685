basePath: /api/v1
definitions:
  model.Cluster:
    properties:
      account_id:
        description: 用户ID，记录谁创建了此集群
        type: string
      cluster_info:
        description: 集群信息，存储AK/SK/logDirectory等
        items:
          type: integer
        type: array
      created_at:
        type: string
      description:
        type: string
      id:
        type: string
      kce_cluster_id:
        description: 外部系统的集群ID
        type: string
      name:
        type: string
      services:
        description: 已安装的服务列表，如["spark", "flink"]
        items:
          type: integer
        type: array
      status:
        description: pending, active, error
        type: string
      updated_at:
        type: string
    type: object
  model.Job:
    properties:
      account_id:
        type: string
      args:
        type: string
      cluster:
        $ref: '#/definitions/model.Cluster'
      cluster_id:
        type: string
      command:
        type: string
      config:
        description: JSON 格式的配置
        type: string
      created_at:
        type: string
      end_time:
        type: string
      id:
        type: string
      logs:
        items:
          type: string
        type: array
      name:
        type: string
      namespace:
        type: string
      runtime:
        description: 运行时长（秒）
        type: integer
      start_time:
        type: string
      status:
        type: string
      type:
        description: spark, flink
        type: string
      updated_at:
        type: string
    type: object
  model.Workflow:
    properties:
      account_id:
        type: string
      cluster:
        $ref: '#/definitions/model.Cluster'
      cluster_id:
        type: string
      created_at:
        type: string
      definition:
        description: JSON 格式的工作流定义
        type: string
      description:
        type: string
      end_time:
        type: string
      id:
        type: string
      name:
        type: string
      namespace:
        type: string
      resource_id:
        description: 工作流资源ID
        type: string
      start_time:
        type: string
      status:
        description: pending, running, completed, failed
        type: string
      updated_at:
        type: string
    type: object
  v1.ArgoWebUIURLResponse:
    properties:
      cluster_id:
        type: string
      description:
        type: string
      web_ui_url:
        type: string
    type: object
  v1.AsyncWorkflowResponse:
    properties:
      argo_workflow:
        type: string
      message:
        type: string
      status:
        type: string
      workflow_id:
        type: string
      workflow_name:
        type: string
    type: object
  v1.CreateClusterAsyncRequest:
    properties:
      account_id:
        type: string
      ak:
        type: string
      description:
        type: string
      kce_cluster_id:
        type: string
      log_directory:
        type: string
      name:
        type: string
      services:
        items:
          type: string
        type: array
      sk:
        type: string
    required:
    - account_id
    - kce_cluster_id
    - name
    type: object
  v1.CreateClusterRequest:
    properties:
      account_id:
        description: 添加 AccountID 字段
        type: string
      ak:
        description: 访问密钥
        type: string
      description:
        type: string
      kce_cluster_id:
        type: string
      log_directory:
        description: 日志存储目录
        type: string
      name:
        type: string
      services:
        description: 需要安装的服务类型列表，如 "spark", "flink" 等，每个服务可能包含多个安装步骤和工作流
        items:
          type: string
        type: array
      sk:
        description: 密钥
        type: string
    required:
    - account_id
    - kce_cluster_id
    - name
    type: object
  v1.CreateClusterResponse:
    properties:
      cluster:
        $ref: '#/definitions/model.Cluster'
      service_errors:
        items:
          type: string
        type: array
      services:
        items:
          type: string
        type: array
    type: object
  v1.CreateJobRequest:
    properties:
      args:
        type: string
      cluster_id:
        type: string
      command:
        type: string
      config:
        additionalProperties: true
        type: object
      job_type:
        type: string
      name:
        type: string
      namespace:
        type: string
    required:
    - cluster_id
    - job_type
    - name
    type: object
  v1.CreateWorkflowRequest:
    properties:
      cluster_id:
        type: string
      definition:
        type: string
      description:
        type: string
      name:
        type: string
      namespace:
        type: string
    required:
    - cluster_id
    - definition
    - name
    type: object
  v1.DeleteClusterAsyncRequest:
    properties:
      account_id:
        type: string
      cluster_id:
        type: string
    required:
    - account_id
    - cluster_id
    type: object
  v1.ErrorResponse:
    properties:
      error:
        type: string
    type: object
  v1.HealthResponse:
    properties:
      service:
        type: string
      status:
        type: string
    type: object
  v1.InstallClusterOperatorRequest:
    properties:
      operator_type:
        type: string
    required:
    - operator_type
    type: object
  v1.OperatorAsyncRequest:
    properties:
      account_id:
        type: string
      cluster_id:
        type: string
      operator_type:
        type: string
    required:
    - account_id
    - cluster_id
    - operator_type
    type: object
  v1.Response:
    properties:
      data: {}
      message:
        type: string
    type: object
  v1.RetryWorkflowRequest:
    properties:
      step_name:
        type: string
    type: object
  v1.UpdateClusterInfoRequest:
    properties:
      ak:
        type: string
      log_directory:
        type: string
      sk:
        type: string
    required:
    - ak
    - log_directory
    - sk
    type: object
  v1.UpdateClusterRequest:
    properties:
      description:
        type: string
      name:
        type: string
    type: object
  v1.UpdateJobRequest:
    properties:
      status:
        type: string
    required:
    - status
    type: object
  v1.UpdateWorkflowRequest:
    properties:
      definition:
        type: string
      description:
        type: string
      name:
        type: string
      namespace:
        type: string
    type: object
  v1.UpdateWorkflowStatusRequest:
    properties:
      status:
        type: string
    required:
    - status
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.kingsoft.com/support
  description: Nimbus 云平台 PaaS 框架 API 服务
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Nimbus API
  version: "1.0"
paths:
  /api/v1/clusters:
    get:
      consumes:
      - application/json
      description: 获取集群列表
      parameters:
      - description: 分页偏移量
        in: query
        name: offset
        type: integer
      - description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 所有者ID
        in: query
        name: account_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/model.Cluster'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 获取集群列表
      tags:
      - 集群
    post:
      consumes:
      - application/json
      description: 创建新集群并可选择部署指定的服务，支持配置AK/SK/logDirectory信息
      parameters:
      - description: 集群信息，包含AK/SK/logDirectory等配置
        in: body
        name: cluster
        required: true
        schema:
          $ref: '#/definitions/v1.CreateClusterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/v1.CreateClusterResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 创建集群
      tags:
      - 集群
  /api/v1/clusters/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的集群，并自动卸载所有已安装的Operator
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 删除集群
      tags:
      - 集群
    get:
      consumes:
      - application/json
      description: 通过ID获取集群信息
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Cluster'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 通过ID获取集群
      tags:
      - 集群
    put:
      consumes:
      - application/json
      description: 更新集群信息
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群信息
        in: body
        name: cluster
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateClusterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Cluster'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 更新集群
      tags:
      - 集群
  /api/v1/clusters/{id}/info:
    put:
      consumes:
      - application/json
      description: 更新集群的AK/SK/logDirectory等配置信息
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群信息
        in: body
        name: clusterInfo
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateClusterInfoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Cluster'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 更新集群信息
      tags:
      - 集群
  /api/v1/clusters/{id}/operators:
    post:
      consumes:
      - application/json
      description: 在指定的集群上安装指定类型的Operator
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: 安装Operator请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.InstallClusterOperatorRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 安装集群Operator
      tags:
      - 集群
  /api/v1/clusters/{id}/operators/{operator_type}:
    delete:
      consumes:
      - application/json
      description: 从指定的集群上卸载指定类型的Operator
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: Operator类型
        in: path
        name: operator_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 卸载集群Operator
      tags:
      - 集群
  /api/v1/clusters/{id}/operators/{operator_type}/status:
    get:
      consumes:
      - application/json
      description: 获取指定集群上指定类型Operator的安装状态
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: Operator类型
        in: path
        name: operator_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 获取集群Operator状态
      tags:
      - 集群
  /api/v1/clusters/{id}/validate:
    post:
      consumes:
      - application/json
      description: 验证指定集群的连接是否正常
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 验证集群连接
      tags:
      - 集群
  /api/v1/health:
    get:
      description: 检查 API 服务是否正常运行
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.HealthResponse'
      summary: 健康检查
      tags:
      - 系统
  /api/v1/jobs:
    get:
      consumes:
      - application/json
      description: 获取作业列表
      parameters:
      - description: 分页偏移量
        in: query
        name: offset
        type: integer
      - description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 应用ID
        in: query
        name: application_id
        type: string
      - description: 集群ID
        in: query
        name: cluster_id
        type: string
      - description: 所有者ID
        in: query
        name: account_id
        type: string
      - description: 作业类型
        in: query
        name: job_type
        type: string
      - description: 作业状态
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/model.Job'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 获取作业列表
      tags:
      - 作业
    post:
      consumes:
      - application/json
      description: 创建新作业
      parameters:
      - description: 作业信息
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/v1.CreateJobRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 创建作业
      tags:
      - 作业
  /api/v1/jobs/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的作业
      parameters:
      - description: 作业ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 删除作业
      tags:
      - 作业
    get:
      consumes:
      - application/json
      description: 通过ID获取作业信息
      parameters:
      - description: 作业ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 通过ID获取作业
      tags:
      - 作业
    put:
      consumes:
      - application/json
      description: 更新作业状态
      parameters:
      - description: 作业ID
        in: path
        name: id
        required: true
        type: string
      - description: 作业状态
        in: body
        name: job
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateJobRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 更新作业
      tags:
      - 作业
  /api/v1/jobs/{id}/cancel:
    post:
      consumes:
      - application/json
      description: 取消正在运行的作业
      parameters:
      - description: 作业ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 取消作业
      tags:
      - 作业
  /api/v1/jobs/{id}/submit:
    post:
      consumes:
      - application/json
      description: 提交作业到集群
      parameters:
      - description: 作业ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 提交作业
      tags:
      - 作业
  /api/v1/workflows:
    get:
      consumes:
      - application/json
      description: 获取工作流列表
      parameters:
      - description: 分页偏移量
        in: query
        name: offset
        type: integer
      - description: 每页数量
        in: query
        name: limit
        type: integer
      - description: 集群ID
        in: query
        name: cluster_id
        type: string
      - description: 所有者ID
        in: query
        name: account_id
        type: string
      - description: 工作流状态
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/model.Workflow'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 获取工作流列表
      tags:
      - 工作流
    post:
      consumes:
      - application/json
      description: 创建新工作流
      parameters:
      - description: 工作流信息
        in: body
        name: workflow
        required: true
        schema:
          $ref: '#/definitions/v1.CreateWorkflowRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/model.Workflow'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 创建工作流
      tags:
      - 工作流
  /api/v1/workflows/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的工作流
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 删除工作流
      tags:
      - 工作流
    get:
      consumes:
      - application/json
      description: 通过ID获取工作流信息
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Workflow'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 通过ID获取工作流
      tags:
      - 工作流
    put:
      consumes:
      - application/json
      description: 更新工作流信息
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      - description: 工作流信息
        in: body
        name: workflow
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateWorkflowRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Workflow'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 更新工作流
      tags:
      - 工作流
  /api/v1/workflows/{id}/cancel:
    post:
      consumes:
      - application/json
      description: 取消正在运行的工作流
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 取消工作流
      tags:
      - 工作流
  /api/v1/workflows/{id}/retry:
    post:
      consumes:
      - application/json
      description: 重试失败的工作流或特定步骤，支持重试整个工作流或单个步骤
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      - description: 重试请求
        in: body
        name: request
        schema:
          $ref: '#/definitions/v1.RetryWorkflowRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 重试工作流
      tags:
      - 工作流
  /api/v1/workflows/{id}/status:
    put:
      consumes:
      - application/json
      description: 更新工作流状态
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      - description: 工作流状态
        in: body
        name: workflow
        required: true
        schema:
          $ref: '#/definitions/v1.UpdateWorkflowStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Workflow'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 更新工作流状态
      tags:
      - 工作流
  /api/v1/workflows/{id}/submit:
    post:
      consumes:
      - application/json
      description: 提交工作流到集群
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 提交工作流
      tags:
      - 工作流
  /api/v1/workflows/{id}/sync:
    post:
      consumes:
      - application/json
      description: 从Argo Workflows同步工作流状态到数据库
      parameters:
      - description: 工作流ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 同步工作流状态
      tags:
      - 工作流
  /api/v1/workflows/argo-webui-url:
    get:
      consumes:
      - application/json
      description: 获取指定集群的 Argo Workflows Web UI 访问地址
      parameters:
      - description: 集群ID
        in: query
        name: cluster_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/v1.ArgoWebUIURLResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 获取 Argo Workflows Web UI URL
      tags:
      - 工作流
  /api/v1/workflows/clusters:
    post:
      consumes:
      - application/json
      description: 异步创建新集群并可选择部署指定的服务，使用Argo Workflows执行，返回工作流ID用于查询进度
      parameters:
      - description: 集群信息
        in: body
        name: cluster
        required: true
        schema:
          $ref: '#/definitions/v1.CreateClusterAsyncRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/v1.AsyncWorkflowResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 异步创建集群
      tags:
      - 工作流
  /api/v1/workflows/clusters/delete:
    post:
      consumes:
      - application/json
      description: 异步删除指定的集群，并自动卸载所有已安装的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
      parameters:
      - description: 删除请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.DeleteClusterAsyncRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/v1.AsyncWorkflowResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 异步删除集群
      tags:
      - 工作流
  /api/v1/workflows/operators/install:
    post:
      consumes:
      - application/json
      description: 在指定的集群上异步安装指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
      parameters:
      - description: 安装请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.OperatorAsyncRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/v1.AsyncWorkflowResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 异步安装Operator
      tags:
      - 工作流
  /api/v1/workflows/operators/uninstall:
    post:
      consumes:
      - application/json
      description: 从指定的集群上异步卸载指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度
      parameters:
      - description: 卸载请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/v1.OperatorAsyncRequest'
      produces:
      - application/json
      responses:
        "202":
          description: Accepted
          schema:
            $ref: '#/definitions/v1.AsyncWorkflowResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/v1.ErrorResponse'
      summary: 异步卸载Operator
      tags:
      - 工作流
swagger: "2.0"
