# 基于 Workflow + Argo 的异步接口架构

## 🎯 设计理念

通过复用现有的 Workflow 和 Argo Workflows 基础设施，实现优雅的异步集群管理解决方案，相比独立的 Task 系统具有显著优势。

## 🏗️ 架构优势

### 相比独立 Task 系统的优势：

1. **复用现有基础设施** - 不重复造轮子，充分利用已有的 Workflow 数据模型和服务层
2. **强大的编排能力** - Argo Workflows 原生支持复杂的多步骤工作流、条件分支、并行执行
3. **可视化监控** - Argo UI 提供直观的工作流执行状态和步骤详情
4. **成熟的重试机制** - Argo 原生支持失败重试、断点续传、手动干预
5. **更好的资源管理** - 基于 Kubernetes 原生资源管理和清理
6. **监控和告警** - 集成 Kubernetes 生态的监控和告警体系

## 📋 核心功能

### 异步集群管理
```bash
# 异步创建集群
POST /api/v1/workflows/clusters
{
    "name": "test-cluster",
    "description": "测试集群",
    "kce_cluster_id": "cluster-123",
    "account_id": "user-123",
    "services": ["spark", "flink"],
    "ak": "access-key",
    "sk": "secret-key",
    "log_directory": "/tmp/logs"
}

# 异步删除集群
POST /api/v1/workflows/clusters/delete
{
    "cluster_id": "uuid",
    "account_id": "user-123"
}
```

### 异步 Operator 管理
```bash
# 异步安装 Operator
POST /api/v1/workflows/operators/install
{
    "cluster_id": "uuid",
    "operator_type": "spark",
    "account_id": "user-123"
}

# 异步卸载 Operator
POST /api/v1/workflows/operators/uninstall
{
    "cluster_id": "uuid",
    "operator_type": "spark",
    "account_id": "user-123"
}
```

### 工作流管理和重试
```bash
# 查询工作流状态
GET /api/v1/workflows/{workflow_id}

# 重试整个工作流
POST /api/v1/workflows/{workflow_id}/retry

# 重试特定步骤
POST /api/v1/workflows/{workflow_id}/retry
{
    "step_name": "install-spark-operator"
}

# 同步 Argo 状态到数据库
POST /api/v1/workflows/{workflow_id}/sync
```

## 🔄 工作流模板

### 集群创建工作流
1. **验证集群连接** - 确保可以连接到 KCE 集群
2. **创建基础资源** - 创建必要的命名空间和配置
3. **并行安装服务** - 根据需求并行安装 Spark、Flink 等 Operator
4. **验证部署状态** - 确认所有组件正常运行
5. **更新集群状态** - 将集群标记为活跃状态

### 集群删除工作流
1. **获取集群信息** - 查询已安装的服务列表
2. **并行卸载 Operators** - 并行卸载所有已安装的 Operator
3. **清理集群资源** - 删除相关命名空间和资源
4. **标记集群已删除** - 更新数据库记录

## ⚡ 重试和恢复机制

### 自动重试
- Argo Workflows 内置重试策略
- 可配置重试次数和间隔
- 支持指数退避算法

### 手动重试
- **重试整个工作流** - 从头开始重新执行
- **重试单个步骤** - 从失败的步骤开始继续
- **断点续传** - 保持已完成步骤的状态

### 失败处理
- 详细的错误信息记录
- 失败步骤的日志查看
- 支持人工干预和修复

## 📊 监控和可观测性

### Argo UI
- 工作流执行状态可视化
- 步骤执行时间和资源使用情况
- 实时日志查看

### 数据库状态
- 工作流状态同步
- 历史执行记录
- 审计日志追踪

## 🚀 使用示例

### 1. 异步创建集群

```bash
# 1. 提交创建工作流
curl -X POST http://localhost:8080/api/v1/workflows/clusters \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-cluster",
    "description": "我的测试集群",
    "kce_cluster_id": "kce-123",
    "account_id": "user-123",
    "services": ["spark"],
    "ak": "ak123",
    "sk": "sk123",
    "log_directory": "/tmp"
  }'

# 响应
{
  "workflow_id": "workflow-uuid",
  "workflow_name": "create-cluster-my-cluster",
  "argo_workflow": "create-cluster-my-cluster-abc123",
  "status": "running",
  "message": "集群创建工作流已提交，请使用workflow_id查询进度"
}

# 2. 查询工作流进度
curl http://localhost:8080/api/v1/workflows/workflow-uuid

# 响应
{
  "id": "workflow-uuid",
  "name": "create-cluster-my-cluster",
  "status": "running",
  "cluster_id": "cluster-uuid",
  "start_time": "2024-01-01T10:00:00Z",
  "steps": [
    {
      "name": "validate-cluster",
      "status": "completed"
    },
    {
      "name": "create-base-resources", 
      "status": "completed"
    },
    {
      "name": "install-spark-operator",
      "status": "running"
    }
  ]
}
```

### 2. 失败重试

```bash
# 查看失败的工作流
curl http://localhost:8080/api/v1/workflows/workflow-uuid

# 响应显示某步骤失败
{
  "status": "failed",
  "steps": [
    {
      "name": "install-spark-operator",
      "status": "failed",
      "error": "网络连接超时"
    }
  ]
}

# 重试失败的步骤
curl -X POST http://localhost:8080/api/v1/workflows/workflow-uuid/retry \
  -H "Content-Type: application/json" \
  -d '{
    "step_name": "install-spark-operator"
  }'

# 或者重试整个工作流
curl -X POST http://localhost:8080/api/v1/workflows/workflow-uuid/retry
```

### 3. 监控工作流

```bash
# 获取工作流列表
curl "http://localhost:8080/api/v1/workflows?limit=20&status=running"

# 同步 Argo 状态
curl -X POST http://localhost:8080/api/v1/workflows/workflow-uuid/sync
```

## 🛠️ 部署配置

### 前置要求
1. **Argo Workflows** - 在目标 K8s 集群安装 Argo Workflows
2. **权限配置** - 确保 Nimbus 有权限操作 Argo 资源
3. **网络连通性** - Nimbus 服务能访问 KCE 集群

### 配置示例

```yaml
# argo-workflows-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: argo-workflows-config
  namespace: argo
data:
  config: |
    namespace: argo
    executor:
      imagePullPolicy: Always
    containerRuntimeExecutor: docker
```

## 🔍 故障排查

### 常见问题
1. **工作流提交失败** - 检查 Argo Workflows 服务状态
2. **步骤执行超时** - 调整超时配置或检查网络
3. **权限错误** - 确认 RBAC 配置正确

### 调试命令
```bash
# 查看 Argo 工作流状态
kubectl get workflows -n argo

# 查看工作流详情
kubectl describe workflow workflow-name -n argo

# 查看工作流日志
kubectl logs -f workflow-name -n argo
```

## 🎉 总结

基于 Workflow + Argo 的异步接口架构提供了：

✅ **更优雅的设计** - 复用现有基础设施  
✅ **更强大的功能** - Argo 的企业级特性  
✅ **更好的可维护性** - 统一的工作流管理  
✅ **更完善的监控** - 可视化执行状态  
✅ **更可靠的重试** - 成熟的错误处理机制  

这种方案不仅满足了异步执行的需求，还提供了比独立 Task 系统更丰富的功能和更好的用户体验。