package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// OperatorService 集群Operator服务接口
type OperatorService interface {
	// InstallOperator 在集群上安装指定类型的Operator
	InstallOperator(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) error

	// UninstallOperator 从集群上卸载指定类型的Operator
	UninstallOperator(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) error

	// IsOperatorInstalled 检查指定类型的Operator是否已安装在集群上
	IsOperatorInstalled(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) (bool, error)
}

// operatorService 集群Operator服务实现
type operatorService struct {
	clusterRepo     repository.ClusterRepository
	auditLogRepo    repository.AuditLogRepository
	operatorFactory *kubernetes.OperatorFactory
}

// NewOperatorService 创建集群Operator服务
func NewOperatorService(
	clusterRepo repository.ClusterRepository,
	auditLogRepo repository.AuditLogRepository,
	operatorFactory *kubernetes.OperatorFactory,
) OperatorService {
	return &operatorService{
		clusterRepo:     clusterRepo,
		auditLogRepo:    auditLogRepo,
		operatorFactory: operatorFactory,
	}
}

// InstallOperator 在集群上安装指定类型的Operator
func (s *operatorService) InstallOperator(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) error {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %w", err)
	}

	// 创建对应类型的Operator
	operator, err := s.operatorFactory.CreateOperator(operatorType)
	if err != nil {
		return fmt.Errorf("创建Operator实例失败: %w", err)
	}

	// 安装Operator
	if err := operator.Install(ctx, cluster.KceClusterID); err != nil {
		return fmt.Errorf("安装Operator失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionCreate, model.AuditResourceOperator,
		fmt.Sprintf("%s:%s", cluster.ID.String(), string(operatorType)),
		fmt.Sprintf("在集群 %s 上安装 %s Operator", cluster.Name, operatorType))

	return nil
}

// UninstallOperator 从集群上卸载指定类型的Operator
func (s *operatorService) UninstallOperator(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) error {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %w", err)
	}

	// 创建对应类型的Operator
	operator, err := s.operatorFactory.CreateOperator(operatorType)
	if err != nil {
		return fmt.Errorf("创建Operator实例失败: %w", err)
	}

	// 卸载Operator
	if err := operator.Uninstall(ctx, cluster.KceClusterID); err != nil {
		return fmt.Errorf("卸载Operator失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionDelete, model.AuditResourceOperator,
		fmt.Sprintf("%s:%s", cluster.ID.String(), string(operatorType)),
		fmt.Sprintf("从集群 %s 上卸载 %s Operator", cluster.Name, operatorType))

	return nil
}

// IsOperatorInstalled 检查指定类型的Operator是否已安装在集群上
func (s *operatorService) IsOperatorInstalled(ctx context.Context, clusterID uuid.UUID, operatorType kubernetes.OperatorType) (bool, error) {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return false, fmt.Errorf("获取集群信息失败: %w", err)
	}

	// 创建对应类型的Operator
	operator, err := s.operatorFactory.CreateOperator(operatorType)
	if err != nil {
		return false, fmt.Errorf("创建Operator实例失败: %w", err)
	}

	// 检查Operator是否已安装
	return operator.IsInstalled(ctx, cluster.KceClusterID)
}

// logAudit 记录审计日志
func (s *operatorService) logAudit(accountID string, action, resource, resourceID, detail string) {
	auditLog := &model.AuditLog{
		AccountID:  accountID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		Detail:     detail,
	}

	if err := s.auditLogRepo.Create(auditLog); err != nil {
		logger.Error("记录审计日志失败", "account_id", accountID, "action", action, "resource", resource, "error", err)
	}
}
