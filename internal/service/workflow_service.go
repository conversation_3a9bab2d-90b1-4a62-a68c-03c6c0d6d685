package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/internal/workflow"
	"github.com/kingsoft/nimbus/pkg/logger"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// WorkflowService 工作流服务接口
type WorkflowService interface {
	Create(name, description, namespace, AccountID string, clusterID uuid.UUID, definition string) (*model.Workflow, error)
	GetByID(id uuid.UUID) (*model.Workflow, error)
	Update(id uuid.UUID, name, description, namespace, definition string) (*model.Workflow, error)
	UpdateStatus(id uuid.UUID, status string) (*model.Workflow, error)
	Delete(id uuid.UUID) error
	List(offset, limit int, clusterID *uuid.UUID, accountID, status string) ([]*model.Workflow, int64, error)
	Submit(ctx context.Context, id uuid.UUID) error
	Cancel(ctx context.Context, id uuid.UUID) error



	// 异步集群操作
	CreateClusterAsync(name, description, kceClusterID, accountID string, services []string, ak, sk, logDirectory string) (*model.Workflow, error)
	DeleteClusterAsync(clusterID uuid.UUID, accountID string) (*model.Workflow, error)
	InstallOperatorAsync(clusterID uuid.UUID, operatorType, accountID string) (*model.Workflow, error)
	UninstallOperatorAsync(clusterID uuid.UUID, operatorType, accountID string) (*model.Workflow, error)

	// 重试机制
	RetryWorkflow(ctx context.Context, workflowID uuid.UUID) error
	RetryWorkflowStep(ctx context.Context, workflowID uuid.UUID, stepName string) error

	// 工作流状态同步
	SyncWorkflowStatus(ctx context.Context, workflowID uuid.UUID) error

	// Argo Workflows Web UI
	GetArgoWebUIURL(clusterID string) (string, error)

	// 新增：Pod监控和清理
	StartPodMonitor(ctx context.Context)
	StopPodMonitor()
	CleanupCompletedPods(ctx context.Context, clusterID string) error
	SyncAllWorkflowStatuses(ctx context.Context) error
}

// workflowService 工作流服务实现
type workflowService struct {
	workflowRepo        repository.WorkflowRepository
	clusterRepo         repository.ClusterRepository
	auditLogRepo        repository.AuditLogRepository
	clientManager       *kubernetes.ClientManager
	argoWorkflowManager *workflow.ArgoWorkflowManager

	// 新增：Pod监控相关
	monitorCtx    context.Context
	monitorCancel context.CancelFunc
	monitorWg     sync.WaitGroup
	monitorMutex  sync.RWMutex
	isMonitoring  bool
}

// NewWorkflowService 创建工作流服务
func NewWorkflowService(
	workflowRepo repository.WorkflowRepository,
	clusterRepo repository.ClusterRepository,
	auditLogRepo repository.AuditLogRepository,
	clientManager *kubernetes.ClientManager,
) WorkflowService {
	argoWorkflowManager := workflow.NewArgoWorkflowManager(clientManager)

	return &workflowService{
		workflowRepo:        workflowRepo,
		clusterRepo:         clusterRepo,
		auditLogRepo:        auditLogRepo,
		clientManager:       clientManager,
		argoWorkflowManager: argoWorkflowManager,
	}
}

// Create 创建工作流
func (s *workflowService) Create(name, description, namespace, AccountID string, clusterID uuid.UUID, definition string) (*model.Workflow, error) {
	// 检查集群是否存在
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 创建工作流
	workflow := &model.Workflow{
		Name:        name,
		Description: description,
		Status:      model.WorkflowStatusPending,
		Definition:  definition,
		ClusterID:   clusterID,
		AccountID:   AccountID,
		Namespace:   namespace,
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		return nil, fmt.Errorf("创建工作流失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(AccountID, model.AuditActionCreate, model.AuditResourceWorkflow, workflow.ID.String(), "创建工作流")

	logger.Info("工作流创建成功", "workflow_id", workflow.ID, "cluster_id", cluster.ID)

	return workflow, nil
}

// GetByID 通过ID获取工作流
func (s *workflowService) GetByID(id uuid.UUID) (*model.Workflow, error) {
	return s.workflowRepo.GetByID(id)
}

// Update 更新工作流信息
func (s *workflowService) Update(id uuid.UUID, name, description, namespace, definition string) (*model.Workflow, error) {
	// 获取工作流
	workflow, err := s.workflowRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("工作流不存在: %w", err)
	}

	// 更新工作流信息
	if name != "" {
		workflow.Name = name
	}
	if description != "" {
		workflow.Description = description
	}
	if namespace != "" {
		workflow.Namespace = namespace
	}
	if definition != "" {
		workflow.Definition = definition
	}

	// 更新工作流
	if err := s.workflowRepo.Update(workflow); err != nil {
		return nil, fmt.Errorf("更新工作流失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(workflow.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, workflow.ID.String(), "更新工作流信息")

	return workflow, nil
}

// UpdateStatus 更新工作流状态
func (s *workflowService) UpdateStatus(id uuid.UUID, status string) (*model.Workflow, error) {
	// 获取工作流
	workflow, err := s.workflowRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("工作流不存在: %w", err)
	}

	// 更新工作流状态
	workflow.Status = status
	if status == model.WorkflowStatusRunning && workflow.StartTime.IsZero() {
		workflow.StartTime = time.Now()
	}
	if (status == model.WorkflowStatusCompleted || status == model.WorkflowStatusFailed || status == model.WorkflowStatusCancelled) && workflow.EndTime.IsZero() {
		workflow.EndTime = time.Now()
	}

	// 更新工作流
	if err := s.workflowRepo.Update(workflow); err != nil {
		return nil, fmt.Errorf("更新工作流状态失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(workflow.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, workflow.ID.String(), fmt.Sprintf("更新工作流状态为%s", status))

	return workflow, nil
}

// Delete 删除工作流
func (s *workflowService) Delete(id uuid.UUID) error {
	// 获取工作流
	workflow, err := s.workflowRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 删除工作流
	if err := s.workflowRepo.Delete(id); err != nil {
		return fmt.Errorf("删除工作流失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(workflow.AccountID, model.AuditActionDelete, model.AuditResourceWorkflow, workflow.ID.String(), "删除工作流")

	return nil
}

// List 获取工作流列表
func (s *workflowService) List(offset, limit int, clusterID *uuid.UUID, accountID, status string) ([]*model.Workflow, int64, error) {
	return s.workflowRepo.List(offset, limit, clusterID, accountID, status)
}

// Submit 提交工作流
func (s *workflowService) Submit(ctx context.Context, id uuid.UUID) error {
	// 获取工作流
	workflow, err := s.workflowRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 获取集群
	cluster, err := s.clusterRepo.GetByID(workflow.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 更新工作流状态
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	// 获取 Kubernetes 客户端
	_, err = s.clientManager.GetClient(ctx, cluster.KceClusterID)
	if err != nil {
		workflow.Status = model.WorkflowStatusFailed
		workflow.EndTime = time.Now()
		if updateErr := s.workflowRepo.Update(workflow); updateErr != nil {
			logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", updateErr)
		}
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 提交工作流到 Kubernetes
	// 注意：这里只是一个示例，实际实现需要根据工作流定义创建 Argo Workflow 资源
	logger.Info("提交工作流到 Kubernetes", "workflow_id", workflow.ID, "cluster_id", cluster.ID, "namespace", workflow.Namespace)

	// 记录审计日志
	s.logAudit(workflow.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, workflow.ID.String(), "提交工作流")

	return nil
}

// Cancel 取消工作流
func (s *workflowService) Cancel(ctx context.Context, id uuid.UUID) error {
	// 获取工作流
	workflow, err := s.workflowRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 获取集群
	cluster, err := s.clusterRepo.GetByID(workflow.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 获取 Kubernetes 客户端
	_, err = s.clientManager.GetClient(ctx, cluster.KceClusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 取消 Kubernetes 中的工作流
	// 注意：这里只是一个示例，实际实现需要根据工作流定义删除 Argo Workflow 资源
	logger.Info("取消 Kubernetes 中的工作流", "workflow_id", workflow.ID, "cluster_id", cluster.ID, "namespace", workflow.Namespace, "resource_id", workflow.ResourceID)

	// 更新工作流状态
	workflow.Status = model.WorkflowStatusCancelled
	workflow.EndTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(workflow.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, workflow.ID.String(), "取消工作流")

	return nil
}



// logAudit 记录审计日志
func (s *workflowService) logAudit(accountID, action, resource, resourceID, detail string) {
	auditLog := &model.AuditLog{
		AccountID:  accountID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		Detail:     detail,
	}

	if err := s.auditLogRepo.Create(auditLog); err != nil {
		logger.Error("记录审计日志失败", "account_id", accountID, "action", action, "resource", resource, "error", err)
	}
}

// CreateClusterAsync 异步创建集群
func (s *workflowService) CreateClusterAsync(name, description, kceClusterID, accountID string, services []string, ak, sk, logDirectory string) (*model.Workflow, error) {
	// 验证参数
	if name == "" || kceClusterID == "" || accountID == "" {
		return nil, fmt.Errorf("必需参数不能为空")
	}

	// 创建临时集群记录（状态为pending）
	clusterService := NewClusterService(s.clusterRepo, s.auditLogRepo, s.clientManager)
	cluster, err := clusterService.Create(name, description, kceClusterID, accountID, ak, sk, logDirectory)
	if err != nil {
		return nil, fmt.Errorf("创建集群记录失败: %w", err)
	}

	// 验证集群连接
	ctx := context.Background()
	client, err := s.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		// 如果连接失败，删除已创建的集群记录
		s.clusterRepo.Delete(cluster.ID)
		return nil, fmt.Errorf("无法连接到KCE集群 %s: %w", kceClusterID, err)
	}

	// 验证集群连接可用性
	_, err = client.Discovery().ServerVersion()
	if err != nil {
		s.clusterRepo.Delete(cluster.ID)
		return nil, fmt.Errorf("KCE集群 %s 不可访问: %w", kceClusterID, err)
	}

	// 创建集群创建工作流定义
	workflowDef := s.argoWorkflowManager.CreateClusterWorkflow("create", cluster.ID.String(), name, kceClusterID, services)

	// 序列化工作流定义
	definitionJSON, err := json.Marshal(workflowDef)
	if err != nil {
		s.clusterRepo.Delete(cluster.ID)
		return nil, fmt.Errorf("序列化工作流定义失败: %w", err)
	}

	// 创建工作流记录
	workflow := &model.Workflow{
		Name:        fmt.Sprintf("create-cluster-%s", name),
		Description: fmt.Sprintf("创建集群: %s", description),
		Status:      model.WorkflowStatusPending,
		Definition:  string(definitionJSON),
		ClusterID:   cluster.ID,
		AccountID:   accountID,
		Namespace:   "argo",
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		s.clusterRepo.Delete(cluster.ID)
		return nil, fmt.Errorf("创建工作流失败: %w", err)
	}

	// 提交工作流到 Argo
	workflowName, err := s.argoWorkflowManager.SubmitWorkflow(ctx, kceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		workflow.Status = model.WorkflowStatusFailed
		workflow.EndTime = time.Now()
		s.workflowRepo.Update(workflow)

		// 记录详细错误信息
		logger.Error("提交工作流到 Argo 失败", "workflow_id", workflow.ID, "cluster_id", cluster.ID, "error", err)
		return nil, fmt.Errorf("提交工作流到 Argo 失败: %w", err)
	}

	// 更新工作流的 ResourceID
	workflow.ResourceID = workflowName
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
		// 尝试取消已提交的 Argo 工作流
		s.argoWorkflowManager.CancelWorkflow(ctx, kceClusterID, workflow.Namespace, workflowName)
	}

	// 记录审计日志
	s.logAudit(accountID, model.AuditActionCreate, model.AuditResourceWorkflow, workflow.ID.String(), "异步创建集群工作流")

	logger.Info("异步集群创建工作流提交成功", "cluster_id", cluster.ID, "workflow_id", workflow.ID, "argo_workflow", workflowName)

	return workflow, nil
}

// DeleteClusterAsync 异步删除集群
func (s *workflowService) DeleteClusterAsync(clusterID uuid.UUID, accountID string) (*model.Workflow, error) {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 创建集群删除工作流定义
	workflowDef := s.argoWorkflowManager.CreateClusterWorkflow("delete", cluster.ID.String(), cluster.Name, cluster.KceClusterID, nil)

	// 序列化工作流定义
	definitionJSON, err := json.Marshal(workflowDef)
	if err != nil {
		return nil, fmt.Errorf("序列化工作流定义失败: %w", err)
	}

	// 创建工作流记录
	workflow := &model.Workflow{
		Name:        fmt.Sprintf("delete-cluster-%s", cluster.Name),
		Description: fmt.Sprintf("异步删除集群: %s", cluster.Description),
		Status:      model.WorkflowStatusPending,
		Definition:  string(definitionJSON),
		ClusterID:   clusterID,
		AccountID:   accountID,
		Namespace:   "argo",
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		return nil, fmt.Errorf("创建工作流失败: %w", err)
	}

	// 提交工作流到 Argo
	ctx := context.Background()
	workflowName, err := s.argoWorkflowManager.SubmitWorkflow(ctx, cluster.KceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		workflow.Status = model.WorkflowStatusFailed
		s.workflowRepo.Update(workflow)
		return nil, fmt.Errorf("提交工作流到 Argo 失败: %w", err)
	}

	// 更新工作流的 ResourceID
	workflow.ResourceID = workflowName
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(accountID, model.AuditActionCreate, model.AuditResourceWorkflow, workflow.ID.String(), "异步删除集群工作流")

	logger.Info("异步集群删除工作流提交成功", "cluster_id", clusterID, "workflow_id", workflow.ID, "argo_workflow", workflowName)

	return workflow, nil
}

// InstallOperatorAsync 异步安装 Operator
func (s *workflowService) InstallOperatorAsync(clusterID uuid.UUID, operatorType, accountID string) (*model.Workflow, error) {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 创建 Operator 安装工作流定义
	workflowDef := s.argoWorkflowManager.CreateOperatorInstallWorkflow(operatorType, cluster.KceClusterID)

	// 序列化工作流定义
	definitionJSON, err := json.Marshal(workflowDef)
	if err != nil {
		return nil, fmt.Errorf("序列化工作流定义失败: %w", err)
	}

	// 创建工作流记录
	workflow := &model.Workflow{
		Name:        fmt.Sprintf("install-%s-operator-%s", operatorType, cluster.Name),
		Description: fmt.Sprintf("异步安装 %s Operator 到集群 %s", operatorType, cluster.Name),
		Status:      model.WorkflowStatusPending,
		Definition:  string(definitionJSON),
		ClusterID:   clusterID,
		AccountID:   accountID,
		Namespace:   "argo",
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		return nil, fmt.Errorf("创建工作流失败: %w", err)
	}

	// 提交工作流到 Argo
	ctx := context.Background()
	workflowName, err := s.argoWorkflowManager.SubmitWorkflow(ctx, cluster.KceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		workflow.Status = model.WorkflowStatusFailed
		s.workflowRepo.Update(workflow)
		return nil, fmt.Errorf("提交工作流到 Argo 失败: %w", err)
	}

	// 更新工作流的 ResourceID
	workflow.ResourceID = workflowName
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(accountID, model.AuditActionCreate, model.AuditResourceWorkflow, workflow.ID.String(), fmt.Sprintf("异步安装 %s Operator 工作流", operatorType))

	logger.Info("异步 Operator 安装工作流提交成功", "cluster_id", clusterID, "operator_type", operatorType, "workflow_id", workflow.ID, "argo_workflow", workflowName)

	return workflow, nil
}

// UninstallOperatorAsync 异步卸载 Operator
func (s *workflowService) UninstallOperatorAsync(clusterID uuid.UUID, operatorType, accountID string) (*model.Workflow, error) {
	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(clusterID)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 创建 Operator 卸载工作流定义（使用类似的模板，但是卸载操作）
	workflowDef := s.argoWorkflowManager.CreateOperatorInstallWorkflow(fmt.Sprintf("uninstall-%s", operatorType), cluster.KceClusterID)

	// 序列化工作流定义
	definitionJSON, err := json.Marshal(workflowDef)
	if err != nil {
		return nil, fmt.Errorf("序列化工作流定义失败: %w", err)
	}

	// 创建工作流记录
	workflow := &model.Workflow{
		Name:        fmt.Sprintf("uninstall-%s-operator-%s", operatorType, cluster.Name),
		Description: fmt.Sprintf("异步卸载 %s Operator 从集群 %s", operatorType, cluster.Name),
		Status:      model.WorkflowStatusPending,
		Definition:  string(definitionJSON),
		ClusterID:   clusterID,
		AccountID:   accountID,
		Namespace:   "argo",
		StartTime:   time.Time{},
		EndTime:     time.Time{},
	}

	if err := s.workflowRepo.Create(workflow); err != nil {
		return nil, fmt.Errorf("创建工作流失败: %w", err)
	}

	// 提交工作流到 Argo
	ctx := context.Background()
	workflowName, err := s.argoWorkflowManager.SubmitWorkflow(ctx, cluster.KceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		workflow.Status = model.WorkflowStatusFailed
		s.workflowRepo.Update(workflow)
		return nil, fmt.Errorf("提交工作流到 Argo 失败: %w", err)
	}

	// 更新工作流的 ResourceID
	workflow.ResourceID = workflowName
	workflow.Status = model.WorkflowStatusRunning
	workflow.StartTime = time.Now()
	if err := s.workflowRepo.Update(workflow); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", workflow.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(accountID, model.AuditActionCreate, model.AuditResourceWorkflow, workflow.ID.String(), fmt.Sprintf("异步卸载 %s Operator 工作流", operatorType))

	logger.Info("异步 Operator 卸载工作流提交成功", "cluster_id", clusterID, "operator_type", operatorType, "workflow_id", workflow.ID, "argo_workflow", workflowName)

	return workflow, nil
}

// RetryWorkflow 重试整个工作流
func (s *workflowService) RetryWorkflow(ctx context.Context, workflowID uuid.UUID) error {
	// 获取工作流
	wf, err := s.workflowRepo.GetByID(workflowID)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(wf.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 取消当前运行的工作流（如果有的话）
	if wf.ResourceID != "" {
		err := s.argoWorkflowManager.CancelWorkflow(ctx, cluster.KceClusterID, wf.Namespace, wf.ResourceID)
		if err != nil {
			logger.Warn("取消旧工作流失败", "workflow_id", workflowID, "argo_workflow", wf.ResourceID, "error", err)
		}
	}

	// 重新解析工作流定义
	var workflowDef workflow.WorkflowDefinition
	if err := json.Unmarshal([]byte(wf.Definition), &workflowDef); err != nil {
		return fmt.Errorf("解析工作流定义失败: %w", err)
	}

	// 重新提交工作流
	workflowName, err := s.argoWorkflowManager.SubmitWorkflow(ctx, cluster.KceClusterID, workflowDef)
	if err != nil {
		// 更新工作流状态为失败
		wf.Status = model.WorkflowStatusFailed
		s.workflowRepo.Update(wf)
		return fmt.Errorf("重新提交工作流失败: %w", err)
	}

	// 更新工作流状态
	wf.ResourceID = workflowName
	wf.Status = model.WorkflowStatusRunning
	wf.StartTime = time.Now()
	wf.EndTime = time.Time{} // 重置结束时间
	if err := s.workflowRepo.Update(wf); err != nil {
		logger.Error("更新工作流状态失败", "workflow_id", wf.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(wf.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, wf.ID.String(), "重试工作流")

	logger.Info("工作流重试成功", "workflow_id", workflowID, "new_argo_workflow", workflowName)

	return nil
}

// RetryWorkflowStep 重试工作流中的特定步骤
func (s *workflowService) RetryWorkflowStep(ctx context.Context, workflowID uuid.UUID, stepName string) error {
	// 获取工作流
	wf, err := s.workflowRepo.GetByID(workflowID)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(wf.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 调用 Argo 工作流管理器重试步骤
	err = s.argoWorkflowManager.RetryWorkflowStep(ctx, cluster.KceClusterID, wf.Namespace, wf.ResourceID, stepName)
	if err != nil {
		return fmt.Errorf("重试工作流步骤失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(wf.AccountID, model.AuditActionUpdate, model.AuditResourceWorkflow, wf.ID.String(), fmt.Sprintf("重试工作流步骤: %s", stepName))

	logger.Info("工作流步骤重试成功", "workflow_id", workflowID, "step_name", stepName)

	return nil
}

// SyncWorkflowStatus 同步工作流状态
func (s *workflowService) SyncWorkflowStatus(ctx context.Context, workflowID uuid.UUID) error {
	// 获取工作流
	wf, err := s.workflowRepo.GetByID(workflowID)
	if err != nil {
		return fmt.Errorf("工作流不存在: %w", err)
	}

	// 如果没有 ResourceID，说明还未提交到 Argo
	if wf.ResourceID == "" {
		return nil
	}

	// 获取集群信息
	cluster, err := s.clusterRepo.GetByID(wf.ClusterID)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 从 Argo 获取工作流状态
	argoStatus, err := s.argoWorkflowManager.GetWorkflowStatus(ctx, cluster.KceClusterID, wf.Namespace, wf.ResourceID)
	if err != nil {
		logger.Error("获取 Argo 工作流状态失败", "workflow_id", workflowID, "argo_workflow", wf.ResourceID, "error", err)
		return err
	}

	// 映射 Argo 状态到内部状态
	var newStatus string
	switch argoStatus {
	case "Pending":
		newStatus = model.WorkflowStatusPending
	case "Running":
		newStatus = model.WorkflowStatusRunning
	case "Succeeded":
		newStatus = model.WorkflowStatusCompleted
	case "Failed", "Error":
		newStatus = model.WorkflowStatusFailed
	default:
		logger.Warn("未知的 Argo 工作流状态", "workflow_id", workflowID, "argo_status", argoStatus)
		return nil
	}

	// 如果状态发生变化，更新数据库
	if wf.Status != newStatus {
		wf.Status = newStatus
		if newStatus == model.WorkflowStatusCompleted || newStatus == model.WorkflowStatusFailed {
			wf.EndTime = time.Now()
		}

		if err := s.workflowRepo.Update(wf); err != nil {
			return fmt.Errorf("更新工作流状态失败: %w", err)
		}

		logger.Info("工作流状态已同步", "workflow_id", workflowID, "old_status", wf.Status, "new_status", newStatus)
	}

	return nil
}

// GetArgoWebUIURL 获取指定集群的 Argo Workflows Web UI URL
func (s *workflowService) GetArgoWebUIURL(clusterID string) (string, error) {
	if clusterID == "" {
		return "", fmt.Errorf("集群ID不能为空")
	}

	// 直接使用 ArgoWorkflowManager 的方法生成 Web UI URL
	url := s.argoWorkflowManager.GetArgoWorkflowsWebUIURL(clusterID)

	logger.Info("生成 Argo Workflows Web UI URL", "cluster_id", clusterID, "url", url)
	return url, nil
}

// StartPodMonitor 启动Pod监控
func (s *workflowService) StartPodMonitor(ctx context.Context) {
	s.monitorMutex.Lock()
	defer s.monitorMutex.Unlock()

	if s.isMonitoring {
		logger.Info("Pod监控已在运行中")
		return
	}

	s.monitorCtx, s.monitorCancel = context.WithCancel(ctx)
	s.isMonitoring = true

	// 启动状态同步监控
	s.monitorWg.Add(1)
	go s.monitorWorkflowStatuses()

	// 启动Pod清理监控
	s.monitorWg.Add(1)
	go s.monitorPodCleanup()

	logger.Info("Pod监控已启动")
}

// StopPodMonitor 停止Pod监控
func (s *workflowService) StopPodMonitor() {
	s.monitorMutex.Lock()
	defer s.monitorMutex.Unlock()

	if !s.isMonitoring {
		return
	}

	if s.monitorCancel != nil {
		s.monitorCancel()
	}

	s.monitorWg.Wait()
	s.isMonitoring = false

	logger.Info("Pod监控已停止")
}

// monitorWorkflowStatuses 监控工作流状态
func (s *workflowService) monitorWorkflowStatuses() {
	defer s.monitorWg.Done()

	// 从配置获取同步间隔，默认30秒
	syncInterval := 30 * time.Second
	ticker := time.NewTicker(syncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.monitorCtx.Done():
			logger.Info("工作流状态监控已停止")
			return
		case <-ticker.C:
			if err := s.SyncAllWorkflowStatuses(s.monitorCtx); err != nil {
				logger.Error("同步工作流状态失败", "error", err)
			}
		}
	}
}

// monitorPodCleanup 监控Pod清理
func (s *workflowService) monitorPodCleanup() {
	defer s.monitorWg.Done()

	// 从配置获取清理间隔，默认60秒
	cleanupInterval := 60 * time.Second
	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.monitorCtx.Done():
			logger.Info("Pod清理监控已停止")
			return
		case <-ticker.C:
			// 获取所有集群并清理每个集群的已完成Pod
			clusters, _, err := s.clusterRepo.List(0, 1000, "")
			if err != nil {
				logger.Error("获取集群列表失败", "error", err)
				continue
			}

			for _, cluster := range clusters {
				if err := s.CleanupCompletedPods(s.monitorCtx, cluster.KceClusterID); err != nil {
					logger.Error("清理集群Pod失败", "cluster_id", cluster.KceClusterID, "error", err)
				}
			}
		}
	}
}

// CleanupCompletedPods 清理已完成的Pod
func (s *workflowService) CleanupCompletedPods(ctx context.Context, clusterID string) error {
	// 获取Kubernetes客户端
	clientset, err := s.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取Kubernetes客户端失败: %w", err)
	}

	// 获取所有命名空间中的Pod
	namespaces := []string{"argo", "default", "nimbus-system", "kmrspark", "kmrflink"}

	for _, namespace := range namespaces {
		pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
			LabelSelector: "app=nimbus", // 只清理nimbus相关的Pod
		})
		if err != nil {
			logger.Error("获取Pod列表失败", "cluster_id", clusterID, "namespace", namespace, "error", err)
			continue
		}

		for _, pod := range pods.Items {
			// 检查Pod是否已完成且超过保留时间
			if s.shouldCleanupPod(&pod) {
				logger.Info("清理已完成的Pod", "cluster_id", clusterID, "namespace", namespace, "pod_name", pod.Name)

				err := clientset.CoreV1().Pods(namespace).Delete(ctx, pod.Name, metav1.DeleteOptions{
					GracePeriodSeconds: &[]int64{0}[0], // 立即删除
				})
				if err != nil {
					logger.Error("删除Pod失败", "cluster_id", clusterID, "namespace", namespace, "pod_name", pod.Name, "error", err)
				}
			}
		}
	}

	return nil
}

// shouldCleanupPod 判断是否应该清理Pod
func (s *workflowService) shouldCleanupPod(pod *corev1.Pod) bool {
	// 只清理已完成的Pod
	if pod.Status.Phase != corev1.PodSucceeded && pod.Status.Phase != corev1.PodFailed {
		return false
	}

	// 检查Pod是否超过保留时间（默认1小时）
	retentionTime := time.Hour
	if pod.Status.ContainerStatuses != nil && len(pod.Status.ContainerStatuses) > 0 {
		containerStatus := pod.Status.ContainerStatuses[0]
		if containerStatus.State.Terminated != nil {
			terminatedTime := containerStatus.State.Terminated.FinishedAt.Time
			if time.Since(terminatedTime) > retentionTime {
				return true
			}
		}
	}

	return false
}

// SyncAllWorkflowStatuses 同步所有工作流状态
func (s *workflowService) SyncAllWorkflowStatuses(ctx context.Context) error {
	// 获取所有运行中的工作流
	workflows, _, err := s.workflowRepo.List(0, 1000, nil, "", "running")
	if err != nil {
		return fmt.Errorf("获取运行中工作流失败: %w", err)
	}

	// 同时获取pending状态的工作流
	pendingWorkflows, _, err := s.workflowRepo.List(0, 1000, nil, "", "pending")
	if err != nil {
		return fmt.Errorf("获取待处理工作流失败: %w", err)
	}

	workflows = append(workflows, pendingWorkflows...)

	for _, wf := range workflows {
		if err := s.SyncWorkflowStatus(ctx, wf.ID); err != nil {
			logger.Error("同步工作流状态失败", "workflow_id", wf.ID, "error", err)
			continue
		}

		// 如果工作流已完成，更新集群状态
		if wf.Status == model.WorkflowStatusCompleted || wf.Status == model.WorkflowStatusFailed {
			if err := s.updateClusterStatusAfterWorkflow(wf); err != nil {
				logger.Error("更新集群状态失败", "workflow_id", wf.ID, "error", err)
			}
		}
	}

	return nil
}

// updateClusterStatusAfterWorkflow 工作流完成后更新集群状态
func (s *workflowService) updateClusterStatusAfterWorkflow(wf *model.Workflow) error {
	cluster, err := s.clusterRepo.GetByID(wf.ClusterID)
	if err != nil {
		return fmt.Errorf("获取集群失败: %w", err)
	}

	// 根据工作流类型和状态更新集群状态
	switch {
	case wf.Name == fmt.Sprintf("create-cluster-%s", cluster.Name):
		if wf.Status == model.WorkflowStatusCompleted {
			cluster.Status = "running"
		} else if wf.Status == model.WorkflowStatusFailed {
			cluster.Status = "failed"
		}
	case wf.Name == fmt.Sprintf("delete-cluster-%s", cluster.Name):
		if wf.Status == model.WorkflowStatusCompleted {
			cluster.Status = "deleted"
		}
	case wf.Name == fmt.Sprintf("install-operator-%s", cluster.Name):
		if wf.Status == model.WorkflowStatusCompleted {
			// 更新集群的operator状态
			// 这里可以根据具体的operator类型进行更新
		}
	case wf.Name == fmt.Sprintf("uninstall-operator-%s", cluster.Name):
		if wf.Status == model.WorkflowStatusCompleted {
			// 更新集群的operator状态
			// 这里可以根据具体的operator类型进行更新
		}
	}

	if err := s.clusterRepo.Update(cluster); err != nil {
		return fmt.Errorf("更新集群状态失败: %w", err)
	}

	logger.Info("集群状态已更新", "cluster_id", cluster.ID, "cluster_name", cluster.Name, "new_status", cluster.Status)

	return nil
}
