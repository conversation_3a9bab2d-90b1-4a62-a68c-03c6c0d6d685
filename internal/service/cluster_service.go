package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// ClusterService 集群服务接口
type ClusterService interface {
	Create(name, description, KceclusterID string, accountID string, ak, sk, logDirectory string) (*model.Cluster, error)
	GetByID(id uuid.UUID) (*model.Cluster, error)
	GetByKceClusterID(clusterID string) (*model.Cluster, error)
	Update(id uuid.UUID, name, description string) (*model.Cluster, error)
	UpdateStatus(id uuid.UUID, status string) error
	UpdateClusterInfo(id uuid.UUID, ak, sk, logDirectory string) (*model.Cluster, error)
	UpdateServices(id uuid.UUID, services []string) error
	Delete(id uuid.UUID) error
	List(offset, limit int, accountID string) ([]*model.Cluster, int64, error)
	ValidateConnection(ctx context.Context, id uuid.UUID) error
}

// clusterService 集群服务实现
type clusterService struct {
	clusterRepo   repository.ClusterRepository
	auditLogRepo  repository.AuditLogRepository
	clientManager *kubernetes.ClientManager
}

// NewClusterService 创建集群服务
func NewClusterService(
	clusterRepo repository.ClusterRepository,
	auditLogRepo repository.AuditLogRepository,
	clientManager *kubernetes.ClientManager,
) ClusterService {
	return &clusterService{
		clusterRepo:   clusterRepo,
		auditLogRepo:  auditLogRepo,
		clientManager: clientManager,
	}
}

// Create 创建集群
func (s *clusterService) Create(name, description, KceClusterID string, accountID string, ak, sk, logDirectory string) (*model.Cluster, error) {
	// 检查集群ID是否已存在
	existingCluster, err := s.clusterRepo.GetByKceClusterID(KceClusterID)
	if err == nil && existingCluster != nil && existingCluster.Status == model.ClusterStatusActive {
		return nil, fmt.Errorf("集群ID已存在")
	}

	// 构建集群信息
	var clusterInfo map[string]interface{}
	if ak != "" && sk != "" && logDirectory != "" {
		clusterInfo = map[string]interface{}{
			"ak":           ak,
			"sk":           sk,
			"logDirectory": logDirectory,
		}
	}

	// 创建集群
	cluster := &model.Cluster{
		Name:         name,
		Description:  description,
		KceClusterID: KceClusterID,
		Status:       model.ClusterStatusPending,
		AccountID:    accountID,
	}

	if err := s.clusterRepo.Create(cluster); err != nil {
		return nil, fmt.Errorf("创建集群失败: %w", err)
	}

	// 如果有集群信息，则更新到数据库
	if clusterInfo != nil {
		if err := s.clusterRepo.UpdateClusterInfo(cluster.ID, clusterInfo); err != nil {
			logger.Error("更新集群信息失败", "cluster_id", cluster.ID, "error", err)
			// 不返回错误，因为集群创建成功，只是信息更新失败
		}
	}

	// 记录审计日志
	s.logAudit(accountID, model.AuditActionCreate, model.AuditResourceCluster, cluster.ID.String(), "创建集群")

	return cluster, nil
}

// UpdateStatus 更新集群状态
func (s *clusterService) UpdateStatus(id uuid.UUID, status string) error {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 更新状态
	if err := s.clusterRepo.UpdateStatus(id, status); err != nil {
		return fmt.Errorf("更新集群状态失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionUpdate, model.AuditResourceCluster, cluster.ID.String(), fmt.Sprintf("更新集群状态为: %s", status))

	return nil
}

// GetByID 通过ID获取集群
func (s *clusterService) GetByID(id uuid.UUID) (*model.Cluster, error) {
	return s.clusterRepo.GetByID(id)
}

// GetByKceClusterID 通过KCE集群ID获取集群
func (s *clusterService) GetByKceClusterID(kceClusterID string) (*model.Cluster, error) {
	return s.clusterRepo.GetByKceClusterID(kceClusterID)
}

// Update 更新集群信息
func (s *clusterService) Update(id uuid.UUID, name, description string) (*model.Cluster, error) {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 更新集群信息
	if name != "" {
		cluster.Name = name
	}
	if description != "" {
		cluster.Description = description
	}

	// 更新集群
	if err := s.clusterRepo.Update(cluster); err != nil {
		return nil, fmt.Errorf("更新集群失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionUpdate, model.AuditResourceCluster, cluster.ID.String(), "更新集群信息")

	return cluster, nil
}

// UpdateClusterInfo 更新集群信息（AK/SK/logDirectory）
func (s *clusterService) UpdateClusterInfo(id uuid.UUID, ak, sk, logDirectory string) (*model.Cluster, error) {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 构建集群信息
	clusterInfo := map[string]interface{}{
		"ak":           ak,
		"sk":           sk,
		"logDirectory": logDirectory,
	}

	// 更新集群信息
	if err := s.clusterRepo.UpdateClusterInfo(id, clusterInfo); err != nil {
		return nil, fmt.Errorf("更新集群信息失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionUpdate, model.AuditResourceCluster, cluster.ID.String(), "更新集群信息（AK/SK/logDirectory）")

	// 返回更新后的集群信息
	return s.clusterRepo.GetByID(id)
}

// UpdateServices 更新集群已安装的服务
func (s *clusterService) UpdateServices(id uuid.UUID, services []string) error {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 更新集群服务
	if err := s.clusterRepo.UpdateServices(id, services); err != nil {
		return fmt.Errorf("更新集群服务失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionUpdate, model.AuditResourceCluster, cluster.ID.String(), fmt.Sprintf("更新集群服务: %v", services))

	return nil
}

// Delete 删除集群
func (s *clusterService) Delete(id uuid.UUID) error {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 删除集群
	if err := s.clusterRepo.Delete(id); err != nil {
		return fmt.Errorf("删除集群失败: %w", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionDelete, model.AuditResourceCluster, cluster.ID.String(), "删除集群")

	return nil
}

// List 获取集群列表
func (s *clusterService) List(offset, limit int, accountID string) ([]*model.Cluster, int64, error) {
	return s.clusterRepo.List(offset, limit, accountID)
}

// ValidateConnection 验证集群连接
func (s *clusterService) ValidateConnection(ctx context.Context, id uuid.UUID) error {
	// 获取集群
	cluster, err := s.clusterRepo.GetByID(id)
	if err != nil {
		return fmt.Errorf("集群不存在: %w", err)
	}

	// 尝试获取客户端
	_, err = s.clientManager.GetClient(ctx, cluster.KceClusterID)
	if err != nil {
		// 更新集群状态为错误
		cluster.Status = model.ClusterStatusError
		if updateErr := s.clusterRepo.Update(cluster); updateErr != nil {
			logger.Error("更新集群状态失败", "cluster_id", cluster.ID, "error", updateErr)
		}
		return fmt.Errorf("验证集群连接失败: %w", err)
	}

	// 更新集群状态为活跃
	cluster.Status = model.ClusterStatusActive
	if err := s.clusterRepo.Update(cluster); err != nil {
		logger.Error("更新集群状态失败", "cluster_id", cluster.ID, "error", err)
	}

	// 记录审计日志
	s.logAudit(cluster.AccountID, model.AuditActionUpdate, model.AuditResourceCluster, cluster.ID.String(), "验证集群连接")

	return nil
}

// logAudit 记录审计日志
func (s *clusterService) logAudit(accountID string, action, resource, resourceID, detail string) {
	auditLog := &model.AuditLog{
		AccountID:  accountID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		Detail:     detail,
	}

	if err := s.auditLogRepo.Create(auditLog); err != nil {
		logger.Error("记录审计日志失败", "account_id", accountID, "action", action, "resource", resource, "error", err)
	}
}
