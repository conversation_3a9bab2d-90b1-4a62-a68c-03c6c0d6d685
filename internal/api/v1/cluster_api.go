package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/kingsoft/nimbus/internal/model"
	"github.com/kingsoft/nimbus/internal/workflow"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/service"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// ClusterAPI 集群 API 处理程序
type ClusterAPI struct {
	clusterService  service.ClusterService
	operatorService service.OperatorService
	workflowService service.WorkflowService
	workflowManager *workflow.ArgoWorkflowManager
}

// NewClusterAPI 创建集群 API 处理程序
func NewClusterAPI(
	clusterService service.ClusterService,
	operatorService service.OperatorService,
	workflowManager *workflow.ArgoWorkflowManager,
) *ClusterAPI {
	return &ClusterAPI{
		clusterService:  clusterService,
		operatorService: operatorService,
		workflowManager: workflowManager,
	}
}

// CreateClusterRequest 创建集群请求
type CreateClusterRequest struct {
	Name         string   `json:"name" binding:"required"`
	Description  string   `json:"description"`
	KceClusterID string   `json:"kce_cluster_id" binding:"required"`
	AccountID    string   `json:"account_id" binding:"required"` // 添加 AccountID 字段
	Services     []string `json:"services"`                      // 需要安装的服务类型列表，如 "spark", "flink" 等，每个服务可能包含多个安装步骤和工作流
	AK           string   `json:"ak"`                            // 访问密钥
	SK           string   `json:"sk"`                            // 密钥
	LogDirectory string   `json:"log_directory"`                 // 日志存储目录
}



// UpdateClusterRequest 更新集群请求
type UpdateClusterRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

// UpdateClusterInfoRequest 更新集群信息请求
type UpdateClusterInfoRequest struct {
	AK           string `json:"ak" binding:"required"`
	SK           string `json:"sk" binding:"required"`
	LogDirectory string `json:"log_directory" binding:"required"`
}

// CreateClusterResponse 创建集群响应
type CreateClusterResponse struct {
	Cluster       *model.Cluster `json:"cluster"`
	Services      []string       `json:"services"`
	ServiceErrors []string       `json:"service_errors,omitempty"`
}

// Create 创建集群
// @Summary 创建集群
// @Description 创建新集群并可选择部署指定的服务，支持配置AK/SK/logDirectory信息
// @Tags 集群
// @Accept json
// @Produce json
// @Param cluster body CreateClusterRequest true "集群信息，包含AK/SK/logDirectory等配置"
// @Success 201 {object} CreateClusterResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters [post]
func (api *ClusterAPI) Create(c *gin.Context) {
	var req CreateClusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	// 创建集群
	cluster, err := api.clusterService.Create(req.Name, req.Description, req.KceClusterID, req.AccountID, req.AK, req.SK, req.LogDirectory)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 如果指定了需要安装的服务，则为每个服务创建服务部署工作流
	var serviceErrors []string
	var installedServices []string

	// 如果指定了需要安装的服务，则直接安装对应的 Operator
	for _, serviceType := range req.Services {
		var operatorType kubernetes.OperatorType

		// 根据服务类型确定 Operator 类型
		switch serviceType {
		case "spark":
			operatorType = kubernetes.OperatorTypeSpark
		case "flink":
			operatorType = kubernetes.OperatorTypeFlink
		default:
			serviceErrors = append(serviceErrors, fmt.Sprintf("不支持的服务类型: %s", serviceType))
			continue
		}

		// 安装 Operator
		ctx := c.Request.Context()
		if err := api.operatorService.InstallOperator(ctx, cluster.ID, operatorType); err != nil {
			serviceErrors = append(serviceErrors, fmt.Sprintf("安装 %s Operator 失败: %s", serviceType, err.Error()))
			continue
		}

		installedServices = append(installedServices, serviceType)
	}

	// 安装 Operator 循环后，更新集群表中的 Services 字段
	if err := api.clusterService.UpdateServices(cluster.ID, installedServices); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "更新集群Services字段失败: " + err.Error()})
		return
	}

	// 返回响应
	response := map[string]interface{}{
		"cluster":  cluster,
		"services": installedServices,
	}

	// 安装 Operator 后，更新集群状态到Active
	if err := api.clusterService.UpdateStatus(cluster.ID, model.ClusterStatusActive); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "更新集群状态失败: " + err.Error()})
		return
	}

	if len(serviceErrors) > 0 {
		response["service_errors"] = serviceErrors
		c.JSON(http.StatusPartialContent, response)
	} else {
		c.JSON(http.StatusCreated, response)
	}
}

// GetByID 通过ID获取集群
// @Summary 通过ID获取集群
// @Description 通过ID获取集群信息
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} model.Cluster
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id} [get]
func (api *ClusterAPI) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	cluster, err := api.clusterService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "集群不存在"})
		return
	}

	c.JSON(http.StatusOK, cluster)
}

// Update 更新集群
// @Summary 更新集群
// @Description 更新集群信息
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param cluster body UpdateClusterRequest true "集群信息"
// @Success 200 {object} model.Cluster
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id} [put]
func (api *ClusterAPI) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	var req UpdateClusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	cluster, err := api.clusterService.Update(id, req.Name, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, cluster)
}

// UpdateClusterInfo 更新集群信息（AK/SK/logDirectory）
// @Summary 更新集群信息
// @Description 更新集群的AK/SK/logDirectory等配置信息
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param clusterInfo body UpdateClusterInfoRequest true "集群信息"
// @Success 200 {object} model.Cluster
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id}/info [put]
func (api *ClusterAPI) UpdateClusterInfo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	var req UpdateClusterInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	// 更新集群信息
	cluster, err := api.clusterService.UpdateClusterInfo(id, req.AK, req.SK, req.LogDirectory)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, cluster)
}

// Delete 删除集群
// @Summary 删除集群
// @Description 删除指定的集群，并自动卸载所有已安装的Operator
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id} [delete]
func (api *ClusterAPI) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	// 获取集群信息，检查已安装的services
	cluster, err := api.clusterService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取集群信息失败: " + err.Error()})
		return
	}

	// 解析已安装的services
	var installedServices []string
	if !cluster.Services.IsNull() {
		// 将JSONString转换为[]string
		servicesStr := cluster.Services.String()
		if servicesStr != "" && servicesStr != "null" {
			// 这里需要将JSON数组字符串解析为[]string
			// 由于JSONString存储的是JSON格式，我们需要解析它
			var services []string
			if err := json.Unmarshal([]byte(servicesStr), &services); err != nil {
				logger.Error("解析集群Services失败", "cluster_id", id, "error", err)
			} else {
				installedServices = services
			}
		}
	}

	// 卸载所有已安装的Operator
	ctx := c.Request.Context()
	var uninstallErrors []string

	for _, service := range installedServices {
		// 将service名称转换为OperatorType
		operatorType := kubernetes.OperatorType(service)

		// 卸载Operator
		if err := api.operatorService.UninstallOperator(ctx, id, operatorType); err != nil {
			errorMsg := fmt.Sprintf("卸载 %s Operator失败: %s", service, err.Error())
			uninstallErrors = append(uninstallErrors, errorMsg)
			logger.Error("卸载Operator失败", "cluster_id", id, "operator_type", service, "error", err)
		} else {
			logger.Info("成功卸载Operator", "cluster_id", id, "operator_type", service)
		}
	}

	// 删除集群
	if err := api.clusterService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "删除集群失败: " + err.Error()})
		return
	}

	// 返回响应
	if len(uninstallErrors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"message": "集群删除成功，但部分Operator卸载失败",
			"errors":  uninstallErrors,
		})
	} else {
		c.JSON(http.StatusOK, Response{Message: "集群删除成功"})
	}
}

// List 获取集群列表
// @Summary 获取集群列表
// @Description 获取集群列表
// @Tags 集群
// @Accept json
// @Produce json
// @Param offset query int false "分页偏移量"
// @Param limit query int false "每页数量"
// @Param account_id query string false "所有者ID"
// @Success 200 {array} model.Cluster
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters [get]
func (api *ClusterAPI) List(c *gin.Context) {
	// 获取分页参数
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "10")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的偏移量参数"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的页大小参数"})
		return
	}

	// 获取过滤条件
	accountID := c.Query("account_id")

	// 获取集群列表
	clusters, total, err := api.clusterService.List(offset, limit, accountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total": total,
		"data":  clusters,
	})
}

// ValidateConnection 验证集群连接
// @Summary 验证集群连接
// @Description 验证指定集群的连接是否正常
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id}/validate [post]
func (api *ClusterAPI) ValidateConnection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	ctx := context.Background()
	if err := api.clusterService.ValidateConnection(ctx, id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "集群连接验证成功"})
}

// InstallClusterOperatorRequest 安装集群Operator请求
type InstallClusterOperatorRequest struct {
	OperatorType string `json:"operator_type" binding:"required"`
}

// InstallClusterOperator 安装集群Operator
// @Summary 安装集群Operator
// @Description 在指定的集群上安装指定类型的Operator
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param request body InstallClusterOperatorRequest true "安装Operator请求"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id}/operators [post]
func (api *ClusterAPI) InstallClusterOperator(c *gin.Context) {
	idStr := c.Param("id")
	clusterID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	var req InstallClusterOperatorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数: " + err.Error()})
		return
	}

	// 转换为OperatorType类型
	operatorType := kubernetes.OperatorType(req.OperatorType)

	// 安装Operator
	ctx := c.Request.Context()
	if err := api.operatorService.InstallOperator(ctx, clusterID, operatorType); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "安装Operator失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{
		Message: fmt.Sprintf("在集群上成功安装 %s Operator", req.OperatorType),
	})
}

// UninstallClusterOperator 卸载集群Operator
// @Summary 卸载集群Operator
// @Description 从指定的集群上卸载指定类型的Operator
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param operator_type path string true "Operator类型"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id}/operators/{operator_type} [delete]
func (api *ClusterAPI) UninstallClusterOperator(c *gin.Context) {
	idStr := c.Param("id")
	clusterID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	operatorTypeStr := c.Param("operator_type")
	operatorType := kubernetes.OperatorType(operatorTypeStr)

	// 卸载Operator
	ctx := c.Request.Context()
	if err := api.operatorService.UninstallOperator(ctx, clusterID, operatorType); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "卸载Operator失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{
		Message: fmt.Sprintf("从集群上成功卸载 %s Operator", operatorTypeStr),
	})
}

// GetClusterOperatorStatus 获取集群Operator状态
// @Summary 获取集群Operator状态
// @Description 获取指定集群上指定类型Operator的安装状态
// @Tags 集群
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param operator_type path string true "Operator类型"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/clusters/{id}/operators/{operator_type}/status [get]
func (api *ClusterAPI) GetClusterOperatorStatus(c *gin.Context) {
	idStr := c.Param("id")
	clusterID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	operatorTypeStr := c.Param("operator_type")
	operatorType := kubernetes.OperatorType(operatorTypeStr)

	// 获取Operator状态
	ctx := c.Request.Context()
	installed, err := api.operatorService.IsOperatorInstalled(ctx, clusterID, operatorType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "获取Operator状态失败: " + err.Error()})
		return
	}

	var status string
	if installed {
		status = "installed"
	} else {
		status = "not_installed"
	}

	c.JSON(http.StatusOK, gin.H{
		"cluster_id":    idStr,
		"operator_type": operatorTypeStr,
		"status":        status,
	})
}


