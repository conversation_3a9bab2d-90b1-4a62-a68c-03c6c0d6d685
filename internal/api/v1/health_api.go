package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthAPI 健康检查 API
type HealthAPI struct{}

// NewHealthAPI 创建健康检查 API 实例
func NewHealthAPI() *HealthAPI {
	return &HealthAPI{}
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
}

// Health 健康检查处理程序
// @Summary 健康检查
// @Description 检查 API 服务是否正常运行
// @Tags 系统
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /api/v1/health [get]
func (api *HealthAPI) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "nimbus-api",
	})
}
