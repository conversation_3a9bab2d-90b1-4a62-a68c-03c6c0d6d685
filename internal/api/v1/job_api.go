package v1

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/service"
)

// JobAPI 作业 API 处理程序
type JobAPI struct {
	jobService service.JobService
}

// NewJobAPI 创建作业 API 处理程序
func NewJobAPI(jobService service.JobService) *JobAPI {
	return &JobAPI{
		jobService: jobService,
	}
}

// CreateJobRequest 创建作业请求
type CreateJobRequest struct {
	Name      string                 `json:"name" binding:"required"`
	ClusterID string                 `json:"cluster_id" binding:"required"`
	JobType   string                 `json:"job_type" binding:"required"`
	Namespace string                 `json:"namespace"`
	Command   string                 `json:"command"`
	Args      string                 `json:"args"`
	Config    map[string]interface{} `json:"config"`
}

// UpdateJobRequest 更新作业请求
type UpdateJobRequest struct {
	Status string `json:"status" binding:"required"`
}

// Create 创建作业
// @Summary 创建作业
// @Description 创建新作业
// @Tags 作业
// @Accept json
// @Produce json
// @Param job body CreateJobRequest true "作业信息"
// @Success 201 {object} model.Job
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs [post]
func (api *JobAPI) Create(c *gin.Context) {
	var req CreateJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	// 从上下文中获取用户ID
	accountID, exists := c.Get("account_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "未授权"})
		return
	}

	// 解析集群ID
	clusterID, err := uuid.Parse(req.ClusterID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
		return
	}

	// 设置默认命名空间
	namespace := req.Namespace
	if namespace == "" {
		namespace = "default"
	}

	job, err := api.jobService.Create(
		req.Name,
		req.JobType,
		namespace,
		accountID.(string),
		clusterID,
		req.Config,
		req.Command,
		req.Args,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, job)
}

// GetByID 通过ID获取作业
// @Summary 通过ID获取作业
// @Description 通过ID获取作业信息
// @Tags 作业
// @Accept json
// @Produce json
// @Param id path string true "作业ID"
// @Success 200 {object} model.Job
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [get]
func (api *JobAPI) GetByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的作业ID"})
		return
	}

	job, err := api.jobService.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: "作业不存在"})
		return
	}

	c.JSON(http.StatusOK, job)
}

// Update 更新作业
// @Summary 更新作业
// @Description 更新作业状态
// @Tags 作业
// @Accept json
// @Produce json
// @Param id path string true "作业ID"
// @Param job body UpdateJobRequest true "作业状态"
// @Success 200 {object} model.Job
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [put]
func (api *JobAPI) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的作业ID"})
		return
	}

	var req UpdateJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的请求参数"})
		return
	}

	job, err := api.jobService.Update(id, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, job)
}

// Delete 删除作业
// @Summary 删除作业
// @Description 删除指定的作业
// @Tags 作业
// @Accept json
// @Produce json
// @Param id path string true "作业ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id} [delete]
func (api *JobAPI) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的作业ID"})
		return
	}

	if err := api.jobService.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "作业删除成功"})
}

// List 获取作业列表
// @Summary 获取作业列表
// @Description 获取作业列表
// @Tags 作业
// @Accept json
// @Produce json
// @Param offset query int false "分页偏移量"
// @Param limit query int false "每页数量"
// @Param application_id query string false "应用ID"
// @Param cluster_id query string false "集群ID"
// @Param account_id query string false "所有者ID"
// @Param job_type query string false "作业类型"
// @Param status query string false "作业状态"
// @Success 200 {array} model.Job
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs [get]
func (api *JobAPI) List(c *gin.Context) {
	// 获取分页参数
	offsetStr := c.DefaultQuery("offset", "0")
	limitStr := c.DefaultQuery("limit", "10")

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的偏移量参数"})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的页大小参数"})
		return
	}

	// 获取过滤条件
	clusterIDStr := c.Query("cluster_id")
	accountID := c.Query("account_id")
	jobType := c.Query("job_type")
	status := c.Query("status")

	// 解析过滤条件
	var clusterID *uuid.UUID

	if clusterIDStr != "" {
		id, err := uuid.Parse(clusterIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的集群ID"})
			return
		}
		clusterID = &id
	}

	// 获取作业列表
	jobs, total, err := api.jobService.List(offset, limit, clusterID, accountID, jobType, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total": total,
		"data":  jobs,
	})
}

// Submit 提交作业
// @Summary 提交作业
// @Description 提交作业到集群
// @Tags 作业
// @Accept json
// @Produce json
// @Param id path string true "作业ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id}/submit [post]
func (api *JobAPI) Submit(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的作业ID"})
		return
	}

	if err := api.jobService.Submit(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "作业提交成功"})
}

// Cancel 取消作业
// @Summary 取消作业
// @Description 取消正在运行的作业
// @Tags 作业
// @Accept json
// @Produce json
// @Param id path string true "作业ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jobs/{id}/cancel [post]
func (api *JobAPI) Cancel(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "无效的作业ID"})
		return
	}

	if err := api.jobService.Cancel(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, Response{Message: "作业取消成功"})
}
