package middleware

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// JWTSecret JWT 密钥
const JWTSecret = "nimbus-secret-key" // 注意：在生产环境中应该从配置或环境变量中获取

// Claims JWT 声明
type Claims struct {
	AccountID string `json:"account_id"`
	Role      string `json:"role"`
	jwt.RegisteredClaims
}

// Auth 认证中间件
func Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头中获取 token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(401, gin.H{"error": "未提供认证令牌"})
			c.Abort()
			return
		}

		// 检查 token 格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.<PERSON>(401, gin.H{"error": "认证令牌格式无效"})
			c.Abort()
			return
		}

		// 解析 token
		tokenString := parts[1]
		claims := &Claims{}
		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
			}
			return []byte(JWTSecret), nil
		})

		if err != nil {
			logger.Error("解析认证令牌失败", "error", err)
			c.JSON(401, gin.H{"error": "认证令牌无效"})
			c.Abort()
			return
		}

		if !token.Valid {
			c.JSON(401, gin.H{"error": "认证令牌无效"})
			c.Abort()
			return
		}

		// 将用户信息存储在上下文中
		c.Set("account_id", claims.AccountID)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// GenerateToken 生成 JWT 令牌
func GenerateToken(accountID string, role string) (string, error) {
	claims := &Claims{
		AccountID: accountID,
		Role:      role,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer: "nimbus",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(JWTSecret))
}
