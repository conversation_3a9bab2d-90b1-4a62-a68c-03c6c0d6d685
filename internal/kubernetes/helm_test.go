package kubernetes

import (
	"github.com/gin-gonic/gin"
	"testing"
)

var region2 = "cn-beijing-6"
var clusterId2 = "a4476a55-33c5-4260-ac48-7e7cc6e7fb61"
var ak2 = "AKLT5bplXymbT4Gxtb5F7CS7ZA"
var sk2 = ""

func TestInstallSparkHistoryHelmChart(t *testing.T) {
	ctx := &gin.Context{}
	ctx.Set("region", region2)
	err := InstallSparkHistoryHelmChart(ctx, clusterId2, ak2, sk2, "ks3://liuchong-test/sparkhistory/")
	if err != nil {
		panic(err)
	}
}
