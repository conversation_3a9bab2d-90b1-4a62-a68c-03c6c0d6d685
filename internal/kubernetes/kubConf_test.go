package kubernetes

import (
	"context"
	"fmt"
	"github.com/kingsoft/nimbus/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	"testing"
)

// 写一个通过kubecfong获取k8s的namespace
const (
	userId    string = "73398439"
	clusterId string = "2a9964c5-d3b9-4b95-8b32-5e752fcbb24a"
	region    string = "cn-northwest-2"
	requestId string = "test-request-id-12345"
)

func TestGetK8sNamespace(t *testing.T) {

	// 获取集群的 kubeconfig
	configByAkSK, err := utils.GetKubernetesConfig(clusterId)
	if err != nil {
		t.Errorf("Failed to get kubeconfig: %v", err)
		return
	}
	fmt.Println("config:\n", string(configByAkSK))

	config, err := clientcmd.RESTConfigFromKubeConfig(configByAkSK)
	if err != nil {
		t.Errorf("Failed to create REST config from kubeconfig: %v", err)
		return
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		t.Errorf("Failed to create Kubernetes client: %v", err)
		return
	}

	namespaces, err := clientset.CoreV1().Namespaces().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		t.Errorf("Failed to list namespaces: %v", err)
		return
	}

	for _, ns := range namespaces.Items {
		fmt.Printf("Namespace: %s\n", ns.Name)
	}
}
