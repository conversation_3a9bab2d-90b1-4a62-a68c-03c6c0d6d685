package kubernetes

import (
	"context"
	"fmt"

	"github.com/kingsoft/nimbus/pkg/logger"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/rest"
)

// JobSubmissionManager 任务提交管理器
type JobSubmissionManager struct {
	clientManager *ClientManager
}

// NewJobSubmissionManager 创建任务提交管理器
func NewJobSubmissionManager(clientManager *ClientManager) *JobSubmissionManager {
	return &JobSubmissionManager{
		clientManager: clientManager,
	}
}

// JobSpec 任务规格
type JobSpec struct {
	JobID      string            `json:"job_id"`
	JobType    string            `json:"job_type"` // spark, flink
	JobName    string            `json:"job_name"`
	Namespace  string            `json:"namespace"`
	Image      string            `json:"image"`
	MainClass  string            `json:"main_class,omitempty"`
	JarFile    string            `json:"jar_file,omitempty"`
	PythonFile string            `json:"python_file,omitempty"`
	Args       []string          `json:"args,omitempty"`
	Env        map[string]string `json:"env,omitempty"`
	Resources  ResourceSpec      `json:"resources"`
	Config     map[string]string `json:"config,omitempty"`
}

// ResourceSpec 资源规格
type ResourceSpec struct {
	DriverCores       string `json:"driver_cores,omitempty"`
	DriverMemory      string `json:"driver_memory,omitempty"`
	ExecutorCores     string `json:"executor_cores,omitempty"`
	ExecutorMemory    string `json:"executor_memory,omitempty"`
	ExecutorInstances int32  `json:"executor_instances,omitempty"`

	// Flink 特定配置
	JobManagerMemory  string `json:"job_manager_memory,omitempty"`
	TaskManagerMemory string `json:"task_manager_memory,omitempty"`
	TaskSlots         int32  `json:"task_slots,omitempty"`
	Parallelism       int32  `json:"parallelism,omitempty"`
}

// SubmitJob 提交任务
func (m *JobSubmissionManager) SubmitJob(ctx context.Context, clusterID string, jobSpec JobSpec) (string, error) {
	switch jobSpec.JobType {
	case "spark":
		return m.submitSparkJob(ctx, clusterID, jobSpec)
	case "flink":
		return m.submitFlinkJob(ctx, clusterID, jobSpec)
	default:
		return "", fmt.Errorf("不支持的任务类型: %s", jobSpec.JobType)
	}
}

// CancelJob 取消任务
func (m *JobSubmissionManager) CancelJob(ctx context.Context, clusterID, jobType, namespace, jobName string) error {
	switch jobType {
	case "spark":
		return m.cancelSparkJob(ctx, clusterID, namespace, jobName)
	case "flink":
		return m.cancelFlinkJob(ctx, clusterID, namespace, jobName)
	default:
		return fmt.Errorf("不支持的任务类型: %s", jobType)
	}
}

// GetJobStatus 获取任务状态
func (m *JobSubmissionManager) GetJobStatus(ctx context.Context, clusterID, jobType, namespace, jobName string) (string, error) {
	switch jobType {
	case "spark":
		return m.getSparkJobStatus(ctx, clusterID, namespace, jobName)
	case "flink":
		return m.getFlinkJobStatus(ctx, clusterID, namespace, jobName)
	default:
		return "", fmt.Errorf("不支持的任务类型: %s", jobType)
	}
}

// submitSparkJob 提交 Spark 任务
func (m *JobSubmissionManager) submitSparkJob(ctx context.Context, clusterID string, jobSpec JobSpec) (string, error) {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 SparkApplication 资源
	sparkAppResource := schema.GroupVersionResource{
		Group:    "sparkoperator.k8s.io",
		Version:  "v1beta2",
		Resource: "sparkapplications",
	}

	// 构建 SparkApplication 对象
	sparkApp := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "sparkoperator.k8s.io/v1beta2",
			"kind":       "SparkApplication",
			"metadata": map[string]interface{}{
				"name":      jobSpec.JobName,
				"namespace": jobSpec.Namespace,
				"labels": map[string]interface{}{
					"app":      "nimbus",
					"job-id":   jobSpec.JobID,
					"job-type": "spark",
				},
			},
			"spec": m.buildSparkApplicationSpec(jobSpec),
		},
	}

	// 创建 SparkApplication
	result, err := dynamicClient.Resource(sparkAppResource).Namespace(jobSpec.Namespace).Create(ctx, sparkApp, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建 SparkApplication 失败: %w", err)
	}

	resourceName := result.GetName()
	logger.Info("Spark 任务提交成功", "cluster_id", clusterID, "job_name", resourceName, "namespace", jobSpec.Namespace)

	return resourceName, nil
}

// submitFlinkJob 提交 Flink 任务
func (m *JobSubmissionManager) submitFlinkJob(ctx context.Context, clusterID string, jobSpec JobSpec) (string, error) {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 FlinkDeployment 资源
	flinkAppResource := schema.GroupVersionResource{
		Group:    "flink.apache.org",
		Version:  "v1beta1",
		Resource: "flinkdeployments",
	}

	// 构建 FlinkDeployment 对象
	flinkApp := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "flink.apache.org/v1beta1",
			"kind":       "FlinkDeployment",
			"metadata": map[string]interface{}{
				"name":      jobSpec.JobName,
				"namespace": jobSpec.Namespace,
				"labels": map[string]interface{}{
					"app":      "nimbus",
					"job-id":   jobSpec.JobID,
					"job-type": "flink",
				},
			},
			"spec": m.buildFlinkDeploymentSpec(jobSpec),
		},
	}

	// 创建 FlinkDeployment
	result, err := dynamicClient.Resource(flinkAppResource).Namespace(jobSpec.Namespace).Create(ctx, flinkApp, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建 FlinkDeployment 失败: %w", err)
	}

	resourceName := result.GetName()
	logger.Info("Flink 任务提交成功", "cluster_id", clusterID, "job_name", resourceName, "namespace", jobSpec.Namespace)

	return resourceName, nil
}

// cancelSparkJob 取消 Spark 任务
func (m *JobSubmissionManager) cancelSparkJob(ctx context.Context, clusterID, namespace, jobName string) error {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 SparkApplication 资源
	sparkAppResource := schema.GroupVersionResource{
		Group:    "sparkoperator.k8s.io",
		Version:  "v1beta2",
		Resource: "sparkapplications",
	}

	// 删除 SparkApplication
	err = dynamicClient.Resource(sparkAppResource).Namespace(namespace).Delete(ctx, jobName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除 SparkApplication 失败: %w", err)
	}

	logger.Info("Spark 任务取消成功", "cluster_id", clusterID, "job_name", jobName, "namespace", namespace)
	return nil
}

// cancelFlinkJob 取消 Flink 任务
func (m *JobSubmissionManager) cancelFlinkJob(ctx context.Context, clusterID, namespace, jobName string) error {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 FlinkDeployment 资源
	flinkAppResource := schema.GroupVersionResource{
		Group:    "flink.apache.org",
		Version:  "v1beta1",
		Resource: "flinkdeployments",
	}

	// 删除 FlinkDeployment
	err = dynamicClient.Resource(flinkAppResource).Namespace(namespace).Delete(ctx, jobName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除 FlinkDeployment 失败: %w", err)
	}

	logger.Info("Flink 任务取消成功", "cluster_id", clusterID, "job_name", jobName, "namespace", namespace)
	return nil
}

// getSparkJobStatus 获取 Spark 任务状态
func (m *JobSubmissionManager) getSparkJobStatus(ctx context.Context, clusterID, namespace, jobName string) (string, error) {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 SparkApplication 资源
	sparkAppResource := schema.GroupVersionResource{
		Group:    "sparkoperator.k8s.io",
		Version:  "v1beta2",
		Resource: "sparkapplications",
	}

	// 获取 SparkApplication
	sparkApp, err := dynamicClient.Resource(sparkAppResource).Namespace(namespace).Get(ctx, jobName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "NotFound", nil
		}
		return "", fmt.Errorf("获取 SparkApplication 失败: %w", err)
	}

	// 提取状态
	status, found, err := unstructured.NestedString(sparkApp.Object, "status", "applicationState", "state")
	if err != nil {
		return "", fmt.Errorf("提取 Spark 任务状态失败: %w", err)
	}

	if !found {
		return "Pending", nil
	}

	return status, nil
}

// getFlinkJobStatus 获取 Flink 任务状态
func (m *JobSubmissionManager) getFlinkJobStatus(ctx context.Context, clusterID, namespace, jobName string) (string, error) {
	// 获取动态客户端
	dynamicClient, err := m.getDynamicClient(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取动态客户端失败: %w", err)
	}

	// 构建 FlinkDeployment 资源
	flinkAppResource := schema.GroupVersionResource{
		Group:    "flink.apache.org",
		Version:  "v1beta1",
		Resource: "flinkdeployments",
	}

	// 获取 FlinkDeployment
	flinkApp, err := dynamicClient.Resource(flinkAppResource).Namespace(namespace).Get(ctx, jobName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "NotFound", nil
		}
		return "", fmt.Errorf("获取 FlinkDeployment 失败: %w", err)
	}

	// 提取状态
	status, found, err := unstructured.NestedString(flinkApp.Object, "status", "lifecycleState")
	if err != nil {
		return "", fmt.Errorf("提取 Flink 任务状态失败: %w", err)
	}

	if !found {
		return "Pending", nil
	}

	return status, nil
}

// getDynamicClient 获取动态客户端
func (m *JobSubmissionManager) getDynamicClient(ctx context.Context, clusterID string) (dynamic.Interface, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建动态客户端失败: %w", err)
	}

	return dynamicClient, nil
}

// getRestConfig 获取 REST 配置
func (m *JobSubmissionManager) getRestConfig(ctx context.Context, clusterID string) (*rest.Config, error) {
	// 通过客户端管理器获取 Kubernetes 客户端，然后提取 REST 配置
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 这里需要从客户端中提取 REST 配置
	// 实际实现中，你可能需要修改 ClientManager 来直接返回 REST 配置
	// 或者重新调用 KCE SDK 来获取配置

	// 暂时返回一个错误，实际实现需要根据 KCE SDK 的具体用法来调整
	_ = client
	return nil, fmt.Errorf("需要实现从 KCE SDK 获取 REST 配置的功能")
}

// buildSparkApplicationSpec 构建 SparkApplication 规格
func (m *JobSubmissionManager) buildSparkApplicationSpec(jobSpec JobSpec) map[string]interface{} {
	driver := map[string]interface{}{
		"cores":          getOrDefault(jobSpec.Resources.DriverCores, "1"),
		"memory":         getOrDefault(jobSpec.Resources.DriverMemory, "1g"),
		"serviceAccount": "spark-operator",
	}

	executor := map[string]interface{}{
		"cores":     getOrDefault(jobSpec.Resources.ExecutorCores, "1"),
		"memory":    getOrDefault(jobSpec.Resources.ExecutorMemory, "1g"),
		"instances": getOrDefaultInt32(jobSpec.Resources.ExecutorInstances, 1),
	}

	spec := map[string]interface{}{
		"type":                "Scala", // 或 "Python"
		"mode":                "cluster",
		"image":               jobSpec.Image,
		"imagePullPolicy":     "Always",
		"mainApplicationFile": jobSpec.JarFile,
		"sparkVersion":        "3.1.1",
		"restartPolicy": map[string]interface{}{
			"type": "Never",
		},
		"driver":   driver,
		"executor": executor,
	}

	if jobSpec.MainClass != "" {
		spec["mainClass"] = jobSpec.MainClass
	}

	if len(jobSpec.Args) > 0 {
		spec["arguments"] = jobSpec.Args
	}

	if jobSpec.Config != nil && len(jobSpec.Config) > 0 {
		spec["sparkConf"] = jobSpec.Config
	}

	return spec
}

// buildFlinkDeploymentSpec 构建 FlinkDeployment 规格
func (m *JobSubmissionManager) buildFlinkDeploymentSpec(jobSpec JobSpec) map[string]interface{} {
	jobManager := map[string]interface{}{
		"memory": getOrDefault(jobSpec.Resources.JobManagerMemory, "1Gi"),
		"cpu":    1,
	}

	taskManager := map[string]interface{}{
		"memory":    getOrDefault(jobSpec.Resources.TaskManagerMemory, "1Gi"),
		"cpu":       1,
		"taskSlots": getOrDefaultInt32(jobSpec.Resources.TaskSlots, 1),
	}

	job := map[string]interface{}{
		"jarURI":      jobSpec.JarFile,
		"parallelism": getOrDefaultInt32(jobSpec.Resources.Parallelism, 1),
		"upgradeMode": "stateless",
	}

	if jobSpec.MainClass != "" {
		job["entryClass"] = jobSpec.MainClass
	}

	if len(jobSpec.Args) > 0 {
		job["args"] = jobSpec.Args
	}

	spec := map[string]interface{}{
		"image":        jobSpec.Image,
		"flinkVersion": "v1_15",
		"flinkConfiguration": map[string]interface{}{
			"taskmanager.numberOfTaskSlots": fmt.Sprintf("%d", getOrDefaultInt32(jobSpec.Resources.TaskSlots, 1)),
		},
		"serviceAccount": "flink-operator",
		"jobManager":     jobManager,
		"taskManager":    taskManager,
		"job":            job,
	}

	if jobSpec.Config != nil && len(jobSpec.Config) > 0 {
		flinkConfig := spec["flinkConfiguration"].(map[string]interface{})
		for k, v := range jobSpec.Config {
			flinkConfig[k] = v
		}
	}

	return spec
}

// 辅助函数
func getOrDefault(value, defaultValue string) string {
	if value == "" {
		return defaultValue
	}
	return value
}

func getOrDefaultInt32(value, defaultValue int32) int32 {
	if value == 0 {
		return defaultValue
	}
	return value
}
