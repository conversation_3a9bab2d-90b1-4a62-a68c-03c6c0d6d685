package kubernetes

import (
	"context"
	"fmt"
	"github.com/kingsoft/nimbus/pkg/utils"
	"sync"

	"github.com/kingsoft/nimbus/pkg/logger"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// ClientManager Kubernetes 客户端管理器
type ClientManager struct {
	clients sync.Map // 存储 clusterID -> *kubernetes.Clientset 的映射
}

// NewClientManager 创建 Kubernetes 客户端管理器
func NewClientManager() *ClientManager {
	return &ClientManager{
		clients: sync.Map{},
	}
}

// GetClient 获取指定集群的 Kubernetes 客户端
func (m *ClientManager) GetClient(ctx context.Context, kceClusterID string) (*kubernetes.Clientset, error) {
	// 检查是否已经有缓存的客户端
	if client, ok := m.clients.Load(kceClusterID); ok {
		return client.(*kubernetes.Clientset), nil
	}

	// 获取集群的 kubeconfig
	kc, err := utils.GetKubernetesConfig(kceClusterID)
	if err != nil {
		logger.Error("获取集群 kubeconfig 失败", "cluster_id", kceClusterID, "error", err)
		return nil, fmt.Errorf("获取集群 kubeconfig 失败, 集群 ID: %s, error: %w", kceClusterID, err)
	}

	// 创建 rest.Config
	config, err := clientcmd.RESTConfigFromKubeConfig(kc)
	if err != nil {
		logger.Error("创建 REST 配置失败", "cluster_id", kceClusterID, "error", err)
		return nil, fmt.Errorf("创建 REST 配置失败: %w", err)
	}

	// 创建 clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		logger.Error("创建 Kubernetes 客户端失败", "cluster_id", kceClusterID, "error", err)
		return nil, fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	// 缓存客户端
	m.clients.Store(kceClusterID, clientset)

	return clientset, nil
}

// GetConfig 获取指定集群的 REST 配置
func (m *ClientManager) GetConfig(ctx context.Context, kceClusterID string) (*rest.Config, error) {
	// 获取集群的 kubeconfig
	kc, err := utils.GetKubernetesConfig(kceClusterID)
	if err != nil {
		logger.Error("获取集群 kubeconfig 失败", "cluster_id", kceClusterID, "error", err)
		return nil, fmt.Errorf("获取集群 kubeconfig 失败, 集群 ID: %s, error: %w", kceClusterID, err)
	}

	// 创建 rest.Config
	config, err := clientcmd.RESTConfigFromKubeConfig(kc)
	if err != nil {
		logger.Error("创建 REST 配置失败", "cluster_id", kceClusterID, "error", err)
		return nil, fmt.Errorf("创建 REST 配置失败: %w", err)
	}

	return config, nil
}

// RemoveClient 从缓存中移除客户端
func (m *ClientManager) RemoveClient(clusterID string) {
	m.clients.Delete(clusterID)
}

// CleanClients 清理所有缓存的客户端
func (m *ClientManager) CleanClients() {
	m.clients = sync.Map{}
}
