package kubernetes

import "context"

// OperatorType 表示支持的Operator类型
type OperatorType string

const (
	// OperatorTypeSpark 代表Spark Operator
	OperatorTypeSpark OperatorType = "spark"
	// OperatorTypeFlink 代表Flink Operator
	OperatorTypeFlink OperatorType = "flink"
	// 未来可以在这里添加更多Operator类型
)

// Operator 定义了所有Operator实现需要的通用接口
type Operator interface {
	// Install 在指定的集群上安装Operator
	Install(ctx context.Context, kce_clusterID string) error

	// Uninstall 从指定的集群上卸载Operator
	Uninstall(ctx context.Context, kce_clusterID string) error

	// IsInstalled 检查Operator是否已经安装在指定的集群上
	IsInstalled(ctx context.Context, kce_clusterID string) (bool, error)

	// GetType 返回Operator的类型
	GetType() OperatorType
}

// OperatorFactory 用于创建不同类型的Operator实例
type OperatorFactory struct {
	clientManager *ClientManager
}

// NewOperatorFactory 创建一个新的OperatorFactory实例
func NewOperatorFactory(clientManager *ClientManager) *OperatorFactory {
	return &OperatorFactory{
		clientManager: clientManager,
	}
}

// CreateOperator 根据指定的类型创建相应的Operator实例
func (f *OperatorFactory) CreateOperator(operatorType OperatorType) (Operator, error) {
	switch operatorType {
	case OperatorTypeSpark:
		return NewSparkOperatorManager(f.clientManager), nil
	case OperatorTypeFlink:
		return NewFlinkOperatorManager(f.clientManager), nil
	default:
		return nil, ErrUnsupportedOperatorType
	}
}

// 错误定义
var (
	ErrUnsupportedOperatorType = KubernetesError("不支持的Operator类型")
)

// KubernetesError 自定义错误类型
type KubernetesError string

func (e KubernetesError) Error() string {
	return string(e)
}
