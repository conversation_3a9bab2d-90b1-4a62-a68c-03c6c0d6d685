package kubernetes

import (
	"context"
	"fmt"
	"time"

	"github.com/kingsoft/nimbus/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
)

const (
	// FlinkOperatorNamespace Flink Operator 的命名空间
	FlinkOperatorNamespace = "kmrflink"
	// FlinkOperatorServiceAccount Flink Operator 的服务账号
	FlinkOperatorServiceAccount = "flink-operator"
	// FlinkOperatorDeploymentName Flink Operator 的部署名称
	FlinkOperatorDeploymentName = "flink-kubernetes-operator"
	// FlinkOperatorImage Flink Operator 的镜像
	FlinkOperatorImage = "ghcr.io/apache/flink-kubernetes-operator:1.6.1"
)

// FlinkOperatorManager Flink Operator 管理器
type FlinkOperatorManager struct {
	clientManager *ClientManager
}

// NewFlinkOperatorManager 创建 Flink Operator 管理器
func NewFlinkOperatorManager(clientManager *ClientManager) *FlinkOperatorManager {
	return &FlinkOperatorManager{
		clientManager: clientManager,
	}
}

// GetType 实现 Operator 接口，返回此操作符的类型
func (m *FlinkOperatorManager) GetType() OperatorType {
	return OperatorTypeFlink
}

// Install 安装 Flink Operator
func (m *FlinkOperatorManager) Install(ctx context.Context, clusterID string) error {
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 创建命名空间
	if err := m.createNamespace(ctx, client); err != nil {
		return err
	}

	// 创建服务账号
	if err := m.createServiceAccount(ctx, client); err != nil {
		return err
	}

	// 创建集群角色和绑定
	if err := m.createRBAC(ctx, client); err != nil {
		return err
	}

	// 部署 Flink Operator
	if err := m.deployOperator(ctx, client); err != nil {
		return err
	}

	// 等待 Flink Operator 就绪
	if err := m.waitForOperatorReady(ctx, client); err != nil {
		return err
	}

	logger.Info("Flink Operator 安装成功", "cluster_id", clusterID)
	return nil
}

// Uninstall 卸载 Flink Operator
func (m *FlinkOperatorManager) Uninstall(ctx context.Context, clusterID string) error {
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 删除部署
	err = client.AppsV1().Deployments(FlinkOperatorNamespace).Delete(ctx, FlinkOperatorDeploymentName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除 Flink Operator 部署失败: %w", err)
	}

	// 删除 RBAC
	err = client.RbacV1().ClusterRoleBindings().Delete(ctx, FlinkOperatorServiceAccount, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除集群角色绑定失败: %w", err)
	}

	err = client.RbacV1().ClusterRoles().Delete(ctx, FlinkOperatorServiceAccount, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除集群角色失败: %w", err)
	}

	// 删除服务账号
	err = client.CoreV1().ServiceAccounts(FlinkOperatorNamespace).Delete(ctx, FlinkOperatorServiceAccount, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除服务账号失败: %w", err)
	}

	// 删除命名空间
	err = client.CoreV1().Namespaces().Delete(ctx, FlinkOperatorNamespace, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除命名空间失败: %w", err)
	}

	logger.Info("Flink Operator 卸载成功", "cluster_id", clusterID)
	return nil
}

// IsInstalled 检查 Flink Operator 是否已安装
func (m *FlinkOperatorManager) IsInstalled(ctx context.Context, clusterID string) (bool, error) {
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return false, fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 检查部署是否存在
	_, err = client.AppsV1().Deployments(FlinkOperatorNamespace).Get(ctx, FlinkOperatorDeploymentName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("检查 Flink Operator 部署失败: %w", err)
	}

	return true, nil
}

// createNamespace 创建命名空间
func (m *FlinkOperatorManager) createNamespace(ctx context.Context, client *kubernetes.Clientset) error {
	namespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: FlinkOperatorNamespace,
		},
	}

	_, err := client.CoreV1().Namespaces().Create(ctx, namespace, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建命名空间失败: %w", err)
	}

	return nil
}

// createServiceAccount 创建服务账号
func (m *FlinkOperatorManager) createServiceAccount(ctx context.Context, client *kubernetes.Clientset) error {
	sa := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      FlinkOperatorServiceAccount,
			Namespace: FlinkOperatorNamespace,
		},
	}

	_, err := client.CoreV1().ServiceAccounts(FlinkOperatorNamespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建服务账号失败: %w", err)
	}

	return nil
}

// createRBAC 创建 RBAC 资源
func (m *FlinkOperatorManager) createRBAC(ctx context.Context, client *kubernetes.Clientset) error {
	// 创建集群角色
	role := &rbacv1.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: FlinkOperatorServiceAccount,
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{""},
				Resources: []string{"pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "serviceaccounts"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"apps"},
				Resources: []string{"deployments", "deployments/finalizers", "replicasets"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"extensions"},
				Resources: []string{"deployments", "ingresses"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"networking.k8s.io"},
				Resources: []string{"ingresses"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"rbac.authorization.k8s.io"},
				Resources: []string{"roles", "rolebindings"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"flink.apache.org"},
				Resources: []string{"flinkdeployments", "flinkdeployments/status", "flinksessionjobs", "flinksessionjobs/status"},
				Verbs:     []string{"*"},
			},
			{
				APIGroups: []string{"autoscaling"},
				Resources: []string{"horizontalpodautoscalers"},
				Verbs:     []string{"*"},
			},
		},
	}

	_, err := client.RbacV1().ClusterRoles().Create(ctx, role, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建集群角色失败: %w", err)
	}

	// 创建集群角色绑定
	binding := &rbacv1.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: FlinkOperatorServiceAccount,
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      "ServiceAccount",
				Name:      FlinkOperatorServiceAccount,
				Namespace: FlinkOperatorNamespace,
			},
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "ClusterRole",
			Name:     FlinkOperatorServiceAccount,
		},
	}

	_, err = client.RbacV1().ClusterRoleBindings().Create(ctx, binding, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建集群角色绑定失败: %w", err)
	}

	return nil
}

// deployOperator 部署 Flink Operator
func (m *FlinkOperatorManager) deployOperator(ctx context.Context, client *kubernetes.Clientset) error {
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      FlinkOperatorDeploymentName,
			Namespace: FlinkOperatorNamespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: int32Ptr(1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": FlinkOperatorDeploymentName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": FlinkOperatorDeploymentName,
					},
				},
				Spec: corev1.PodSpec{
					ServiceAccountName: FlinkOperatorServiceAccount,
					Containers: []corev1.Container{
						{
							Name:  "flink-operator",
							Image: FlinkOperatorImage,
							Env: []corev1.EnvVar{
								{
									Name:  "FLINK_OPERATOR_NAMESPACE",
									Value: FlinkOperatorNamespace,
								},
								{
									Name:  "FLINK_OPERATOR_SERVICE_ACCOUNT",
									Value: FlinkOperatorServiceAccount,
								},
							},
							Ports: []corev1.ContainerPort{
								{
									Name:          "metrics",
									ContainerPort: 8080,
								},
							},
						},
					},
				},
			},
		},
	}

	_, err := client.AppsV1().Deployments(FlinkOperatorNamespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建 Flink Operator 部署失败: %w", err)
	}

	return nil
}

// waitForOperatorReady 等待 Flink Operator 就绪
func (m *FlinkOperatorManager) waitForOperatorReady(ctx context.Context, client *kubernetes.Clientset) error {
	return wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		deployment, err := client.AppsV1().Deployments(FlinkOperatorNamespace).Get(ctx, FlinkOperatorDeploymentName, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				return false, nil
			}
			return false, err
		}

		if deployment.Status.ReadyReplicas >= 1 {
			return true, nil
		}

		return false, nil
	})
}
