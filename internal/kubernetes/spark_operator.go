package kubernetes

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/kingsoft/nimbus/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
)

const (
	// SparkOperatorNamespace Spark Operator 的命名空间
	SparkOperatorNamespace = "kmrspark"
	// SparkOperatorServiceAccount Spark Operator 的服务账号
	SparkOperatorServiceAccount = "kmrspark"
	// SparkOperatorDeploymentName Spark Operator 的部署名称
	SparkOperatorDeploymentName = "spark-operator"
	// SparkHistoryDeploymentName Spark History 的部署名称
	SparkHistoryDeploymentName = "spark-history"
)

// SparkOperatorManager Spark Operator 管理器
type SparkOperatorManager struct {
	clientManager *ClientManager
}

// NewSparkOperatorManager 创建 Spark Operator 管理器
func NewSparkOperatorManager(clientManager *ClientManager) *SparkOperatorManager {
	return &SparkOperatorManager{
		clientManager: clientManager,
	}
}

// GetType 实现 Operator 接口，返回此操作符的类型
func (m *SparkOperatorManager) GetType() OperatorType {
	return OperatorTypeSpark
}

// Install 安装 Spark Operator
func (m *SparkOperatorManager) Install(ctx context.Context, kce_clusterID string) error {
	client, err := m.clientManager.GetClient(ctx, kce_clusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 创建命名空间
	if err := m.createNamespace(ctx, client); err != nil {
		return err
	}

	// 创建服务账号
	if err := m.createServiceAccount(ctx, client); err != nil {
		return err
	}

	// 创建集群角色和绑定
	if err := m.createRBAC(ctx, client); err != nil {
		return err
	}

	// 部署 Spark Operator
	if err := m.deployOperator(ctx, client, kce_clusterID); err != nil {
		return err
	}

	// 等待 Spark Operator 就绪
	if err := m.waitForOperatorReady(ctx, client); err != nil {
		return err
	}

	logger.Info("Spark Operator 安装成功", "cluster_id", kce_clusterID)
	return nil
}

// createNamespace 创建命名空间
func (m *SparkOperatorManager) createNamespace(ctx context.Context, client *kubernetes.Clientset) error {
	namespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: SparkOperatorNamespace,
		},
	}

	_, err := client.CoreV1().Namespaces().Create(ctx, namespace, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建命名空间失败: %w", err)
	}

	return nil
}

// createServiceAccount 创建服务账号
func (m *SparkOperatorManager) createServiceAccount(ctx context.Context, client *kubernetes.Clientset) error {
	sa := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      SparkOperatorServiceAccount,
			Namespace: SparkOperatorNamespace,
		},
	}

	_, err := client.CoreV1().ServiceAccounts(SparkOperatorNamespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建服务账号失败: %w", err)
	}

	return nil
}

// createRBAC 创建 RBAC 资源
func (m *SparkOperatorManager) createRBAC(ctx context.Context, client *kubernetes.Clientset) error {
	// 创建Role - 用于删除spark相关资源
	role := &rbacv1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: SparkOperatorNamespace,
			Name:      "spark-resources-deleter-role",
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: []string{""},
				Resources: []string{"services", "persistentvolumeclaims", "pods", "configmaps", "secrets", "jobs", "deployments", "statefulsets", "jobs", "persistentvolumes"},
				Verbs:     []string{"*"},
			},
		},
	}

	_, err := client.RbacV1().Roles(SparkOperatorNamespace).Create(ctx, role, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建Spark资源删除角色失败: %w", err)
	}

	// 创建RoleBinding - 将角色绑定到服务账户
	roleBinding := &rbacv1.RoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "spark-resources-deleter-rolebinding",
			Namespace: SparkOperatorNamespace,
		},
		Subjects: []rbacv1.Subject{
			{
				Kind:      "ServiceAccount",
				Name:      SparkOperatorServiceAccount,
				Namespace: SparkOperatorNamespace,
			},
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "Role",
			Name:     "spark-resources-deleter-role",
		},
	}

	_, err = client.RbacV1().RoleBindings(SparkOperatorNamespace).Create(ctx, roleBinding, metav1.CreateOptions{})
	if err != nil && !errors.IsAlreadyExists(err) {
		return fmt.Errorf("创建Spark资源删除角色绑定失败: %w", err)
	}

	return nil
}

// Uninstall 卸载 Spark Operator
func (m *SparkOperatorManager) Uninstall(ctx context.Context, clusterID string) error {
	// 获取集群的 kubeconfig
	kubeConfig, err := utils.GetKubernetesConfig(clusterID)
	if err != nil {
		return fmt.Errorf("获取集群 kubeconfig 失败: %w", err)
	}

	// 使用Helm卸载Spark Operator
	err = m.clientManager.UninstallHelmRelease(kubeConfig, SparkOperatorDeploymentName, SparkOperatorNamespace)
	if err != nil {
		return fmt.Errorf("使用Helm卸载Spark Operator失败: %w", err)
	}
	// 使用Helm卸载Spark History
	err = m.clientManager.UninstallHelmRelease(kubeConfig, SparkHistoryDeploymentName, SparkOperatorNamespace)
	if err != nil {
		return fmt.Errorf("使用Helm卸载Spark history失败: %w", err)
	}
	logger.Info("Spark 卸载成功", "cluster_id", clusterID)
	return nil
}

// IsInstalled 检查 Spark Operator 是否已安装
func (m *SparkOperatorManager) IsInstalled(ctx context.Context, clusterID string) (bool, error) {
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return false, fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 检查部署是否存在
	_, err = client.AppsV1().Deployments(SparkOperatorNamespace).Get(ctx, SparkOperatorDeploymentName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("检查 Spark Operator 部署失败: %w", err)
	}

	return true, nil
}

// deployOperator 部署 Spark Operator
func (m *SparkOperatorManager) deployOperator(ctx context.Context, client *kubernetes.Clientset, kce_clusterID string) error {
	// 获取集群的 kubeconfig
	kubeConfig, err := utils.GetKubernetesConfig(kce_clusterID)
	if err != nil {
		return fmt.Errorf("获取集群 kubeconfig 失败: %w", err)
	}
	// 使用Helm安装Spark Operator
	err = installSparkOperatoByHelm(kubeConfig)
	if err != nil {
		return fmt.Errorf("installSparkOperatoByHelm 失败: %w", err)
	}
	// 安装Spark History
	// TODO 使用AKSK 安装Spark History 组装路径
	err = InstallSparkHistoryByHelm(ctx, kubeConfig, kce_clusterID)
	if err != nil {
		return fmt.Errorf("InstallSparkHistoryByHelm 失败: %w", err)
	}

	logger.Info("Spark Operator 安装成功", "cluster_id", kce_clusterID)
	return nil

}

func InstallSparkHistoryByHelm(ctx context.Context, kubeConfig []byte, kce_clusterID string) error {
	// 从数据库获取集群信息
	clusterRepo := repository.NewClusterRepository()
	cluster, err := clusterRepo.GetByKceClusterID(kce_clusterID)
	if err != nil {
		return fmt.Errorf("获取集群信息失败: %w", err)
	}

	// 解析集群信息
	var clusterInfo map[string]interface{}
	if cluster.ClusterInfo.IsNull() {
		return fmt.Errorf("集群信息为空，请先配置AK/SK/logDirectory")
	}

	if err := json.Unmarshal(cluster.ClusterInfo, &clusterInfo); err != nil {
		return fmt.Errorf("解析集群信息失败: %w", err)
	}

	// 获取AK/SK/logDirectory
	ak, ok := clusterInfo["ak"].(string)
	if !ok || ak == "" {
		return fmt.Errorf("集群信息中缺少AK配置")
	}

	sk, ok := clusterInfo["sk"].(string)
	if !ok || sk == "" {
		return fmt.Errorf("集群信息中缺少SK配置")
	}

	logDirectory, ok := clusterInfo["logDirectory"].(string)
	if !ok || logDirectory == "" {
		return fmt.Errorf("集群信息中缺少logDirectory配置")
	}

	// 获取当前文件所在目录，然后计算chart路径
	_, currentFile, _, _ := runtime.Caller(0)
	projectRoot := filepath.Join(filepath.Dir(currentFile), "..", "..")
	chartPath := filepath.Join(projectRoot, "deploy", "helm", SparkHistoryDeploymentName)

	valuesTpl := `
fullName: spark-history
rbac:
  roleName: spark
  roleBindingName: spark
replicaCount: 1
image:
  repository: hub.kce.ksyun.com/bigdata-platform/pyspark
  tag: v3.4.3-ksc1.3
  pullPolicy: Always
ks3:
  endpoint: %s
  AccessKey: %s
  AccessSecret: %s
  logDirectory: %s
service:
  name: http
  externalPort: 80
  internalPort: 18080
  type: ClusterIP
ingress:
  host: %s
  ingressClass: kmr-on-kce
resources: {}
nodeSelector: {}
tolerations: []
`

	ingressHost := strings.TrimPrefix(utils.GetSparkHistoryServerUrl(kce_clusterID), "https://")
	logHistoryPath := utils.GetLogHistoryPath(kce_clusterID)
	ks3logDirectory := fmt.Sprintf(logDirectory+"/%s", logHistoryPath)
	values := fmt.Sprintf(valuesTpl, "ks3-cn-beijing-internal.ksyuncs.com", ak, sk, ks3logDirectory, ingressHost)

	// 使用Helm安装Spark History
	err = InstallHelmChartFromLocal(
		kubeConfig,
		SparkOperatorNamespace,     // namespace
		chartPath,                  // 本地chart路径
		SparkHistoryDeploymentName, // releaseName
		"",                         // version - 使用最新版本
		values,                     // values - 使用默认values.yaml
	)
	if err != nil {
		return fmt.Errorf("使用Helm安装Spark History失败: %w", err)
	}
	return nil
}

func installSparkOperatoByHelm(kubeConfig []byte) error {
	// spark-operator
	// 获取当前文件所在目录，然后计算chart路径
	_, currentFile, _, _ := runtime.Caller(0)
	projectRoot := filepath.Join(filepath.Dir(currentFile), "..", "..")
	chartPath := filepath.Join(projectRoot, "deploy", "helm", SparkOperatorDeploymentName)

	// 使用Helm安装Spark Operator，使用本地chart路径
	err := InstallHelmChartFromLocal(
		kubeConfig,
		SparkOperatorNamespace,      // namespace
		chartPath,                   // 本地chart路径
		SparkOperatorDeploymentName, // releaseName
		"",                          // version - 使用最新版本
		"",                          // values - 使用默认values.yaml
	)
	if err != nil {
		return fmt.Errorf("使用Helm安装Spark Operator失败: %w", err)
	}
	return nil
}

// waitForOperatorReady 等待 Spark Operator 就绪
func (m *SparkOperatorManager) waitForOperatorReady(ctx context.Context, client *kubernetes.Clientset) error {
	return wait.PollImmediate(5*time.Second, 2*time.Minute, func() (bool, error) {
		deployment, err := client.AppsV1().Deployments(SparkOperatorNamespace).Get(ctx, SparkOperatorDeploymentName, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				return false, nil
			}
			return false, err
		}

		if deployment.Status.ReadyReplicas >= 1 {
			return true, nil
		}

		return false, nil
	})
}

// int32Ptr 返回 int32 指针
func int32Ptr(i int32) *int32 {
	return &i
}
