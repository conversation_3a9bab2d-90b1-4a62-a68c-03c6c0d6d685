package kubernetes

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/kingsoft/nimbus/pkg/utils"
	"github.com/mittwald/go-helm-client"
	"helm.sh/helm/v3/pkg/repo"
	"strings"
)

func InstallHelmChart(kubeConfig []byte, namespace, repoName, repoURL, releaseName, version, values string) error {
	opt := &helmclient.KubeConfClientOptions{
		Options: &helmclient.Options{
			Namespace:        namespace,
			RepositoryCache:  "/tmp/.helmcache",
			RepositoryConfig: "/tmp/.helmrepo",
			Debug:            true,
		},
		KubeContext: "",
		KubeConfig:  kubeConfig,
	}
	helmClient, err := helmclient.NewClientFromKubeConf(opt)
	if err != nil {
		return fmt.Errorf("failed to initialize helm client: [%s]", err)
	}
	if err = helmClient.AddOrUpdateChartRepo(repo.Entry{Name: repoName, URL: repoURL}); err != nil {
		return fmt.Errorf("add or update chart repo error: [%s]", err)
	}

	chartSpec := helmclient.ChartSpec{
		ChartName:       repoName + "/" + releaseName,
		Version:         version,
		Namespace:       namespace,
		ReleaseName:     releaseName,
		CreateNamespace: true,
		ValuesYaml:      values,
	}

	if _, err = helmClient.InstallOrUpgradeChart(context.Background(), &chartSpec, nil); err != nil {
		return fmt.Errorf("install chart error: [%s]", err)
	}
	return nil
}

// InstallHelmChartFromLocal 从本地 chart 目录安装 Helm chart
func InstallHelmChartFromLocal(kubeConfig []byte, namespace, chartPath, releaseName, version, values string) error {
	opt := &helmclient.KubeConfClientOptions{
		Options: &helmclient.Options{
			Namespace:        namespace,
			RepositoryCache:  "/tmp/.helmcache",
			RepositoryConfig: "/tmp/.helmrepo",
			Debug:            true,
		},
		KubeContext: "",
		KubeConfig:  kubeConfig,
	}
	helmClient, err := helmclient.NewClientFromKubeConf(opt)
	if err != nil {
		return fmt.Errorf("failed to initialize helm client: [%s]", err)
	}

	chartSpec := helmclient.ChartSpec{
		ChartName:       chartPath,
		Version:         version,
		Namespace:       namespace,
		ReleaseName:     releaseName,
		CreateNamespace: true,
		ValuesYaml:      values,
	}

	if _, err = helmClient.InstallOrUpgradeChart(context.Background(), &chartSpec, nil); err != nil {
		return fmt.Errorf("install chart error: [%s]", err)
	}
	return nil
}

// 添加Helm卸载方法
func (m *ClientManager) UninstallHelmRelease(kubeConfig []byte, releaseName, namespace string) error {
	opt := &helmclient.KubeConfClientOptions{
		Options: &helmclient.Options{
			Namespace:        namespace,
			RepositoryCache:  "/tmp/.helmcache",
			RepositoryConfig: "/tmp/.helmrepo",
			Debug:            true,
		},
		KubeContext: "",
		KubeConfig:  kubeConfig,
	}

	helmClient, err := helmclient.NewClientFromKubeConf(opt)
	if err != nil {
		return fmt.Errorf("failed to initialize helm client: %w", err)
	}

	// 卸载release
	if err := helmClient.UninstallReleaseByName(releaseName); err != nil {
		return fmt.Errorf("uninstall chart error: %w", err)
	}

	return nil
}

func InstallSparkHistoryHelmChart(c *gin.Context, clusterId, ak, sk, logDirectory string) error {
	kubeConfig, err := utils.GetKubernetesConfig(clusterId)
	if err != nil {
		return fmt.Errorf("InstallSparkHistoryHelmChart: %w", err)
	}
	valuesTpl := `
fullName: spark-history
rbac:
  roleName: spark
  roleBindingName: spark
replicaCount: 1
image:
  repository: hub.kce.ksyun.com/bigdata-platform/pyspark
  tag: v3.4.3-ksc1.3
  pullPolicy: Always
ks3:
  endpoint: %s
  AccessKey: %s
  AccessSecret: %s
  logDirectory: %s
service:
  name: http
  externalPort: 80
  internalPort: 18080
  type: ClusterIP
ingress:
  host: %s
  ingressClass: kmr-on-kce
resources: {}
nodeSelector: {}
tolerations: []
`

	region := c.GetString("region")
	ks3endpoint, _, err := utils.GetKs3EndPointByRegion(region)
	if err != nil {
		return fmt.Errorf("InstallSparkHistoryHelmChart: %w", err)
	}
	ingressHost := strings.TrimPrefix(utils.GetSparkHistoryServerUrl(clusterId), "https://")
	values := fmt.Sprintf(valuesTpl, ks3endpoint, ak, sk, logDirectory, ingressHost)
	repoName := fmt.Sprintf("bigdata-platform-%s", region)
	repoUrl := fmt.Sprintf("https://hub-vpc-%s.kce.ksyun.com/chartrepo/bigdata-platform", region)
	version := "0.1.3"
	if err = InstallHelmChart(kubeConfig, "kmrspark", repoName, repoUrl, "spark-history", version, values); err != nil {
		return fmt.Errorf("InstallSparkHistoryHelmChart: %w", err)
	}
	return nil
}
