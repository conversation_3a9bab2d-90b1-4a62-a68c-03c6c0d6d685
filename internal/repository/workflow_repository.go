package repository

import (
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/model"
)

// WorkflowRepository 工作流仓库接口
type WorkflowRepository interface {
	Create(workflow *model.Workflow) error
	GetByID(id uuid.UUID) (*model.Workflow, error)
	Update(workflow *model.Workflow) error
	Delete(id uuid.UUID) error
	List(offset, limit int, clusterID *uuid.UUID, accountID, status string) ([]*model.Workflow, int64, error)
}

// workflowRepository 工作流仓库实现
type workflowRepository struct{}

// NewWorkflowRepository 创建工作流仓库
func NewWorkflowRepository() WorkflowRepository {
	return &workflowRepository{}
}

// Create 创建工作流
func (r *workflowRepository) Create(workflow *model.Workflow) error {
	return DB.Create(workflow).Error
}

// GetByID 通过ID获取工作流
func (r *workflowRepository) GetByID(id uuid.UUID) (*model.Workflow, error) {
	var workflow model.Workflow
	if err := DB.Preload("Cluster").First(&workflow, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &workflow, nil
}

// Update 更新工作流
func (r *workflowRepository) Update(workflow *model.Workflow) error {
	return DB.Save(workflow).Error
}

// Delete 删除工作流
func (r *workflowRepository) Delete(id uuid.UUID) error {
	return DB.Delete(&model.Workflow{}, "id = ?", id).Error
}

// List 获取工作流列表
func (r *workflowRepository) List(offset, limit int, clusterID *uuid.UUID, accountID, status string) ([]*model.Workflow, int64, error) {
	var workflows []*model.Workflow
	var total int64
	query := DB.Model(&model.Workflow{}).Preload("Cluster")

	if clusterID != nil {
		query = query.Where("cluster_id = ?", clusterID)
	}

	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&workflows).Error; err != nil {
		return nil, 0, err
	}

	return workflows, total, nil
}
