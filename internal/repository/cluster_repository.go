package repository

import (
	"encoding/json"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/model"
)

// ClusterRepository 集群仓库接口
type ClusterRepository interface {
	Create(cluster *model.Cluster) error
	GetByID(id uuid.UUID) (*model.Cluster, error)
	GetByKceClusterID(kceClusterID string) (*model.Cluster, error)
	Update(cluster *model.Cluster) error
	UpdateStatus(id uuid.UUID, status string) error
	UpdateServices(id uuid.UUID, services []string) error
	UpdateClusterInfo(id uuid.UUID, clusterInfo map[string]interface{}) error
	Delete(id uuid.UUID) error
	List(offset, limit int, accountID string) ([]*model.Cluster, int64, error)
}

// clusterRepository 集群仓库实现
type clusterRepository struct{}

// NewClusterRepository 创建集群仓库
func NewClusterRepository() ClusterRepository {
	return &clusterRepository{}
}

// Create 创建集群
func (r *clusterRepository) Create(cluster *model.Cluster) error {
	return DB.Create(cluster).Error
}

// GetByID 通过ID获取集群
func (r *clusterRepository) GetByID(id uuid.UUID) (*model.Cluster, error) {
	var cluster model.Cluster
	if err := DB.Where("id = ? AND status != ?", id, model.ClusterStatusDeleted).First(&cluster).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

// GetByKceClusterID 通过外部集群ID获取集群
func (r *clusterRepository) GetByKceClusterID(kceClusterID string) (*model.Cluster, error) {
	var cluster model.Cluster
	if err := DB.Where("kce_cluster_id = ? AND status != ?", kceClusterID, model.ClusterStatusDeleted).First(&cluster).Error; err != nil {
		return nil, err
	}
	return &cluster, nil
}

// Update 更新集群
func (r *clusterRepository) Update(cluster *model.Cluster) error {
	return DB.Save(cluster).Error
}

// UpdateStatus 更新集群状态
func (r *clusterRepository) UpdateStatus(id uuid.UUID, status string) error {
	return DB.Model(&model.Cluster{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateServices 更新已安装的Service列表
func (r *clusterRepository) UpdateServices(id uuid.UUID, services []string) error {
	servicesJSON, err := json.Marshal(services)
	if err != nil {
		return err
	}
	return DB.Model(&model.Cluster{}).Where("id = ?", id).Update("services", servicesJSON).Error
}

// UpdateClusterInfo 更新集群信息
func (r *clusterRepository) UpdateClusterInfo(id uuid.UUID, clusterInfo map[string]interface{}) error {
	clusterInfoJSON, err := json.Marshal(clusterInfo)
	if err != nil {
		return err
	}
	return DB.Model(&model.Cluster{}).Where("id = ?", id).Update("cluster_info", clusterInfoJSON).Error
}

// Delete 删除集群（软删除，只更新状态为deleted）
func (r *clusterRepository) Delete(id uuid.UUID) error {
	return DB.Model(&model.Cluster{}).Where("id = ?", id).Update("status", model.ClusterStatusDeleted).Error
}

// List 获取集群列表
func (r *clusterRepository) List(offset, limit int, accountID string) ([]*model.Cluster, int64, error) {
	var clusters []*model.Cluster
	var total int64

	query := DB.Model(&model.Cluster{}).Where("status != ?", model.ClusterStatusDeleted)

	// 根据用户ID过滤
	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&clusters).Error; err != nil {
		return nil, 0, err
	}

	return clusters, total, nil
}
