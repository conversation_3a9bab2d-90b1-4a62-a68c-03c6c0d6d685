package repository

import (
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/model"
)

// AuditLogRepository 审计日志仓库接口
type AuditLogRepository interface {
	Create(log *model.AuditLog) error
	GetByID(id uuid.UUID) (*model.AuditLog, error)
	List(offset, limit int, accountID string, action, resource string) ([]*model.AuditLog, int64, error)
}

// auditLogRepository 审计日志仓库实现
type auditLogRepository struct{}

// NewAuditLogRepository 创建审计日志仓库
func NewAuditLogRepository() AuditLogRepository {
	return &auditLogRepository{}
}

// Create 创建审计日志
func (r *auditLogRepository) Create(log *model.AuditLog) error {
	return DB.Create(log).Error
}

// GetByID 通过ID获取审计日志
func (r *auditLogRepository) GetByID(id uuid.UUID) (*model.AuditLog, error) {
	var log model.AuditLog
	if err := DB.Preload("User").First(&log, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &log, nil
}

// List 获取审计日志列表
func (r *auditLogRepository) List(offset, limit int, accountID string, action, resource string) ([]*model.AuditLog, int64, error) {
	var logs []*model.AuditLog
	var total int64
	query := DB.Model(&model.AuditLog{}).Preload("User")

	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	if action != "" {
		query = query.Where("action = ?", action)
	}

	if resource != "" {
		query = query.Where("resource = ?", resource)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
