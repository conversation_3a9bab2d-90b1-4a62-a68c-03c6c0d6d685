package repository

import (
	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/internal/model"
)

// JobRepository 任务仓库接口
type JobRepository interface {
	Create(job *model.Job) error
	GetByID(id uuid.UUID) (*model.Job, error)
	Update(job *model.Job) error
	Delete(id uuid.UUID) error
	List(offset, limit int, clusterID *uuid.UUID, accountID, jobType, status string) ([]*model.Job, int64, error)
}

// jobRepository 任务仓库实现
type jobRepository struct{}

// NewJobRepository 创建任务仓库
func NewJobRepository() JobRepository {
	return &jobRepository{}
}

// Create 创建任务
func (r *jobRepository) Create(job *model.Job) error {
	return DB.Create(job).Error
}

// GetByID 通过ID获取任务
func (r *jobRepository) GetByID(id uuid.UUID) (*model.Job, error) {
	var job model.Job
	if err := DB.Preload("Application").Preload("Cluster").First(&job, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &job, nil
}

// Update 更新任务
func (r *jobRepository) Update(job *model.Job) error {
	return DB.Save(job).Error
}

// Delete 删除任务
func (r *jobRepository) Delete(id uuid.UUID) error {
	return DB.Delete(&model.Job{}, "id = ?", id).Error
}

// List 获取任务列表
func (r *jobRepository) List(offset, limit int, clusterID *uuid.UUID, accountID, jobType, status string) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64
	query := DB.Model(&model.Job{}).Preload("Cluster")

	if clusterID != nil {
		query = query.Where("cluster_id = ?", clusterID)
	}

	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	if jobType != "" {
		query = query.Where("type = ?", jobType)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&jobs).Error; err != nil {
		return nil, 0, err
	}

	return jobs, total, nil
}
