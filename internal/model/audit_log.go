package model

// AuditLog 审计日志模型
type AuditLog struct {
	BaseModel
	AccountID  string `gorm:"type:char(36)" json:"account_id"`
	Action     string `gorm:"size:50;not null" json:"action"`   // create, update, delete, login, etc.
	Resource   string `gorm:"size:50;not null" json:"resource"` // user, cluster, application, job, workflow
	ResourceID string `gorm:"size:36" json:"resource_id"`
	Detail     string `gorm:"type:text" json:"detail"` // 存储JSON格式的详细信息
	ClientIP   string `gorm:"size:50" json:"client_ip"`
	UserAgent  string `gorm:"size:200" json:"user_agent"`
}

// AuditAction 审计动作常量
const (
	AuditActionCreate = "create"
	AuditActionUpdate = "update"
	AuditActionDelete = "delete"
	AuditActionLogin  = "login"
	AuditActionLogout = "logout"
)

// AuditResource 审计资源常量
const (
	AuditResourceUser        = "user"
	AuditResourceCluster     = "cluster"
	AuditResourceApplication = "application"
	AuditResourceJob         = "job"
	AuditResourceWorkflow    = "workflow"
	AuditResourceOperator    = "operator"
)

// TableName 指定表名
func (AuditLog) TableName() string {
	return "audit_logs"
}
