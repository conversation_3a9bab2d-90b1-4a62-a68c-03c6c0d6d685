package model

import (
	"database/sql/driver"
	"errors"
)

// Cluster Kubernetes集群模型
type Cluster struct {
	BaseModel
	Name         string     `gorm:"size:100;not null" json:"name"`
	Description  string     `gorm:"size:500" json:"description"`
	KceClusterID string     `gorm:"size:100;not null" json:"kce_cluster_id"` // 外部系统的集群ID
	Status       string     `gorm:"size:20;default:'pending'" json:"status"` // pending, active, error
	AccountID    string     `gorm:"type:char(36)" json:"account_id"`         // 用户ID，记录谁创建了此集群
	Services     JSONString `gorm:"type:json" json:"services"`               // 已安装的服务列表，如["spark", "flink"]
	ClusterInfo  JSONString `gorm:"type:json" json:"cluster_info"`           // 集群信息，存储AK/SK/logDirectory等
}

// ClusterStatus 集群状态常量
const (
	ClusterStatusPending = "pending"
	ClusterStatusActive  = "active"
	ClusterStatusError   = "error"
	ClusterStatusDeleted = "deleted"
)

// JSONString 自定义JSON类型
type JSONString []byte

// Value 实现driver.Valuer接口
func (j JSONString) Value() (driver.Value, error) {
	if j.IsNull() {
		return nil, nil
	}
	return string(j), nil
}

// Scan 实现sql.Scanner接口
func (j *JSONString) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	s, ok := value.([]byte)
	if !ok {
		return errors.New("invalid scan source")
	}
	*j = append((*j)[0:0], s...)
	return nil
}

// MarshalJSON 实现json.Marshaler接口
func (j JSONString) MarshalJSON() ([]byte, error) {
	if j.IsNull() {
		return []byte("null"), nil
	}
	return j, nil
}

// UnmarshalJSON 实现json.Unmarshaler接口
func (j *JSONString) UnmarshalJSON(data []byte) error {
	if j == nil {
		return errors.New("null point exception")
	}
	*j = append((*j)[0:0], data...)
	return nil
}

// IsNull 判断是否为空
func (j JSONString) IsNull() bool {
	return len(j) == 0 || string(j) == "null"
}

// String 转换为字符串
func (j JSONString) String() string {
	return string(j)
}

// TableName 指定表名
func (Cluster) TableName() string {
	return "clusters"
}
