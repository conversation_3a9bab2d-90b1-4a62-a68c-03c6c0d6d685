package workflow

import (
	"fmt"
)

// CreateClusterWorkflow 创建集群工作流
func (m *ArgoWorkflowManager) CreateClusterWorkflow(clusterID, clusterName, kceClusterID, services string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      fmt.Sprintf("create-cluster-%s", clusterID[:8]),
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"workflow-type": "cluster-create",
			"cluster-id":    clusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint: "create-cluster",
			Arguments: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id", Value: clusterID},
					{Name: "cluster-name", Value: clusterName},
					{Name: "kce-cluster-id", Value: kceClusterID},
					{Name: "services", Value: services},
				},
			},
			Templates: []WorkflowTemplate{
				// 主工作流模板
				{
					Name: "create-cluster",
					Steps: [][]WorkflowStep{
						{
							{
								Name:     "install-spark-operator",
								Template: "install-spark-operator",
								Arguments: &WorkflowStepArguments{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
										{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
									},
								},
							},
						},
						{
							{
								Name:     "install-flink-operator",
								Template: "install-flink-operator",
								Arguments: &WorkflowStepArguments{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
										{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
									},
								},
							},
						},
						{
							{
								Name:     "install-spark-history",
								Template: "install-spark-history",
								Arguments: &WorkflowStepArguments{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
										{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
									},
								},
							},
						},
						{
							{
								Name:     "verify-services",
								Template: "verify-services",
								Arguments: &WorkflowStepArguments{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
										{Name: "services", Value: "{{workflow.parameters.services}}"},
									},
								},
							},
						},
						{
							{
								Name:     "update-cluster-status",
								Template: "update-cluster-status",
								Arguments: &WorkflowStepArguments{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
										{Name: "services", Value: "{{workflow.parameters.services}}"},
									},
								},
							},
						},
					},
				},
				// Spark Operator 安装模板
				{
					Name: "install-spark-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Spark Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall spark-operator -n kmrspark || true
kubectl delete namespace kmrspark || true

# 全新安装Spark Operator
echo "全新安装Spark Operator..."
helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --create-namespace \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --wait --timeout=10m

echo "✓ Spark Operator 安装完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
							{Name: "kce-cluster-id"},
						},
					},
				},
				// Flink Operator 安装模板
				{
					Name: "install-flink-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Flink Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall flink-operator -n kmrflink || true
kubectl delete namespace kmrflink || true

# 全新安装Flink Operator
echo "全新安装Flink Operator..."
helm install flink-operator kmr-on-kce/flink-operator \
  --namespace kmrflink \
  --create-namespace \
  --set image.repository=ghcr.io/apache/flink-kubernetes-operator \
  --set image.tag=1.6.1 \
  --wait --timeout=10m

echo "✓ Flink Operator 安装完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
							{Name: "kce-cluster-id"},
						},
					},
				},
				// Spark History 安装模板
				{
					Name: "install-spark-history",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Spark History Server..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 检查是否已经安装了Spark History
if helm list -n kmrspark | grep -q "spark-history"; then
    echo "Spark History Server已存在，跳过安装"
    exit 0
fi

# 配置参数
CLUSTER_ID="{{inputs.parameters.cluster-id}}"
AK="test-access-key"
SK="test-secret-key" 
SPARK_LOG_DIR="ks3://spark-logs/history/${CLUSTER_ID}"
INGRESS_HOST="spark-history-${CLUSTER_ID}.kmr-on-kce.ksyun.com"

echo "配置信息："
echo "  Cluster ID: $CLUSTER_ID"
echo "  Log Directory: $SPARK_LOG_DIR"
echo "  Ingress Host: $INGRESS_HOST"

# 安装Spark History Server
echo "安装Spark History Server..."
helm install spark-history kmr-on-kce/spark-history \
  --namespace kmrspark \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/pyspark \
  --set image.tag=v3.4.3-ksc1.3 \
  --set ks3.endpoint=ks3-cn-beijing-internal.ksyuncs.com \
  --set ks3.AccessKey="$AK" \
  --set ks3.AccessSecret="$SK" \
  --set ks3.logDirectory="$SPARK_LOG_DIR" \
  --set ingress.enabled=true \
  --set ingress.host="$INGRESS_HOST" \
  --set service.type=ClusterIP \
  --set service.port=18080 \
  --wait --timeout=10m

echo "✓ Spark History Server 安装完成"

# 验证安装
echo "验证Spark History Server安装..."
kubectl get deployment spark-history -n kmrspark
kubectl get service spark-history -n kmrspark
kubectl get ingress -n kmrspark
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
							{Name: "kce-cluster-id"},
						},
					},
				},
				// 验证服务模板
				{
					Name: "verify-services",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "验证服务安装状态..."

# 检查 Spark Operator
echo "验证 Spark Operator 部署状态..."
kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
echo "✓ Spark Operator 验证成功"

# 检查 Flink Operator
echo "验证 Flink Operator 部署状态..."
kubectl wait --for=condition=available --timeout=300s deployment/flink-kubernetes-operator -n kmrflink
echo "✓ Flink Operator 验证成功"

# 检查Spark History（如果安装了）
if kubectl get deployment spark-history -n kmrspark >/dev/null 2>&1; then
    echo "验证 Spark History 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/spark-history -n kmrspark
    echo "✓ Spark History 验证成功"
else
    echo "⚠ Spark History 未安装，跳过验证"
fi

echo "✓ 所有服务验证完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
							{Name: "services"},
						},
					},
				},
				// 更新集群状态模板
				{
					Name: "update-cluster-status",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "更新集群状态为活跃..."

# 创建集群状态 ConfigMap
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-status
  namespace: nimbus-system
data:
  status: "active"
  updated-at: "$(date -Iseconds)"
  services: "{{inputs.parameters.services}}"
EOF

echo "✓ 集群状态更新完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
							{Name: "services"},
						},
					},
				},
			},
		},
	}
}

// kubectlInClusterSetupShell kubectl 集群内配置脚本
const kubectlInClusterSetupShell = `
# 配置 kubectl 使用 in-cluster ServiceAccount 凭证
if [ -n "$KUBERNETES_SERVICE_HOST" ] && [ -f "/var/run/secrets/kubernetes.io/serviceaccount/token" ]; then
  echo "[setup] 配置 kubectl 使用 in-cluster ServiceAccount 凭证"
  export K8S_HOST="https://${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT:-443}"
  cat >/tmp/kubeconfig <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    certificate-authority: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    server: ${K8S_HOST}
  name: in-cluster
contexts:
- context:
    cluster: in-cluster
    user: in-cluster-user
  name: in-cluster
current-context: in-cluster
users:
- name: in-cluster-user
  user:
    tokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
EOF
  export KUBECONFIG=/tmp/kubeconfig
  echo "[setup] kubectl 配置完成"
else
  echo "[setup] 未检测到 in-cluster 环境，使用默认配置"
fi
`
