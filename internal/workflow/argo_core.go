package workflow

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/kingsoft/nimbus/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

const (
	// ArgoWorkflowGroup Argo Workflow 的 API Group
	ArgoWorkflowGroup = "argoproj.io"
	// ArgoWorkflowVersion Argo Workflow 的 API Version
	ArgoWorkflowVersion = "v1alpha1"
	// ArgoWorkflowResource Argo Workflow 的资源名
	ArgoWorkflowResource = "workflows"
	// ArgoWorkflowNamespace Argo Workflow 的默认命名空间
	ArgoWorkflowNamespace = "argo"
)

// WorkflowManager 工作流管理器接口
type WorkflowManager interface {
	// 基础工作流操作
	SubmitWorkflow(ctx context.Context, kceClusterID string, workflowDef WorkflowDefinition) (string, error)
	GetWorkflow(ctx context.Context, clusterID, namespace, workflowName string) (*unstructured.Unstructured, error)
	ListWorkflows(ctx context.Context, clusterID, namespace string) ([]WorkflowInfo, error)
	CancelWorkflow(ctx context.Context, clusterID, namespace, workflowName string) error
	RetryWorkflow(ctx context.Context, clusterID, namespace, workflowName, stepName string) error
	
	// 集群管理工作流
	CreateClusterWorkflow(clusterID, clusterName, kceClusterID, services string) WorkflowDefinition
	DeleteClusterWorkflow(clusterID, clusterName, kceClusterID string) WorkflowDefinition
}

// ArgoWorkflowManager Argo 工作流管理器
type ArgoWorkflowManager struct {
	clientManager utils.KubernetesClientManager
}

// NewArgoWorkflowManager 创建新的 Argo 工作流管理器
func NewArgoWorkflowManager(clientManager utils.KubernetesClientManager) *ArgoWorkflowManager {
	return &ArgoWorkflowManager{
		clientManager: clientManager,
	}
}

// WorkflowInfo 工作流信息
type WorkflowInfo struct {
	Name      string    `json:"name"`
	Namespace string    `json:"namespace"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	StartedAt time.Time `json:"started_at"`
	FinishedAt time.Time `json:"finished_at"`
}

// WorkflowDefinition 工作流定义
type WorkflowDefinition struct {
	Name        string                 `json:"name"`
	Namespace   string                 `json:"namespace"`
	Labels      map[string]string      `json:"labels,omitempty"`
	Annotations map[string]string      `json:"annotations,omitempty"`
	Spec        WorkflowSpec           `json:"spec"`
}

// WorkflowSpec 工作流规格
type WorkflowSpec struct {
	Entrypoint string             `json:"entrypoint"`
	Templates  []WorkflowTemplate `json:"templates"`
	Arguments  *WorkflowInputs    `json:"arguments,omitempty"`
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	Name     string               `json:"name"`
	Steps    [][]WorkflowStep     `json:"steps,omitempty"`
	Script   *WorkflowScriptSpec  `json:"script,omitempty"`
	Inputs   *WorkflowInputs      `json:"inputs,omitempty"`
	Outputs  *WorkflowOutputs     `json:"outputs,omitempty"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	Name      string                    `json:"name"`
	Template  string                    `json:"template"`
	Arguments *WorkflowStepArguments    `json:"arguments,omitempty"`
}

// WorkflowStepArguments 工作流步骤参数
type WorkflowStepArguments struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowScriptSpec 脚本规格
type WorkflowScriptSpec struct {
	Image   string   `json:"image"`
	Command []string `json:"command"`
	Source  string   `json:"source"`
}

// WorkflowInputs 工作流输入
type WorkflowInputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowOutputs 工作流输出
type WorkflowOutputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowParameter 工作流参数
type WorkflowParameter struct {
	Name  string `json:"name"`
	Value string `json:"value,omitempty"`
}

// SubmitWorkflow 提交工作流到 Argo
func (m *ArgoWorkflowManager) SubmitWorkflow(ctx context.Context, kceClusterID string, workflowDef WorkflowDefinition) (string, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 确保 Argo 命名空间存在
	client, err := m.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	if err := m.ensureArgoNamespace(ctx, client, workflowDef.Namespace); err != nil {
		return "", fmt.Errorf("确保 Argo 命名空间失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 生成唯一的工作流名称
	if workflowDef.Name == "" {
		workflowDef.Name = fmt.Sprintf("wf-%s", uuid.New().String()[:8])
	}

	// 设置默认命名空间
	if workflowDef.Namespace == "" {
		workflowDef.Namespace = ArgoWorkflowNamespace
	}

	// 构建 Unstructured 对象
	workflow := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": fmt.Sprintf("%s/%s", ArgoWorkflowGroup, ArgoWorkflowVersion),
			"kind":       "Workflow",
			"metadata": map[string]interface{}{
				"name":        workflowDef.Name,
				"namespace":   workflowDef.Namespace,
				"labels":      workflowDef.Labels,
				"annotations": workflowDef.Annotations,
			},
			"spec": workflowDef.Spec,
		},
	}

	// 提交工作流
	result, err := dynamicClient.Resource(workflowResource).Namespace(workflowDef.Namespace).Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建工作流失败: %w", err)
	}

	workflowName := result.GetName()
	logger.Info("工作流提交成功", "workflow_name", workflowName, "namespace", workflowDef.Namespace)

	return workflowName, nil
}

// GetWorkflow 获取工作流
func (m *ArgoWorkflowManager) GetWorkflow(ctx context.Context, clusterID, namespace, workflowName string) (*unstructured.Unstructured, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取工作流失败: %w", err)
	}

	return workflow, nil
}

// ListWorkflows 列出工作流
func (m *ArgoWorkflowManager) ListWorkflows(ctx context.Context, clusterID, namespace string) ([]WorkflowInfo, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 列出工作流
	workflowList, err := dynamicClient.Resource(workflowResource).Namespace(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("列出工作流失败: %w", err)
	}

	var workflows []WorkflowInfo
	for _, item := range workflowList.Items {
		workflow := WorkflowInfo{
			Name:      item.GetName(),
			Namespace: item.GetNamespace(),
		}

		// 解析状态
		if status, found, err := unstructured.NestedString(item.Object, "status", "phase"); err == nil && found {
			workflow.Status = status
		}

		// 解析时间
		if createdAt := item.GetCreationTimestamp(); !createdAt.IsZero() {
			workflow.CreatedAt = createdAt.Time
		}

		if startedAt, found, err := unstructured.NestedString(item.Object, "status", "startedAt"); err == nil && found {
			if t, err := time.Parse(time.RFC3339, startedAt); err == nil {
				workflow.StartedAt = t
			}
		}

		if finishedAt, found, err := unstructured.NestedString(item.Object, "status", "finishedAt"); err == nil && found {
			if t, err := time.Parse(time.RFC3339, finishedAt); err == nil {
				workflow.FinishedAt = t
			}
		}

		workflows = append(workflows, workflow)
	}

	return workflows, nil
}

// CancelWorkflow 取消工作流
func (m *ArgoWorkflowManager) CancelWorkflow(ctx context.Context, clusterID, namespace, workflowName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 删除工作流
	err = dynamicClient.Resource(workflowResource).Namespace(namespace).Delete(ctx, workflowName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除工作流失败: %w", err)
	}

	logger.Info("工作流取消成功", "workflow_name", workflowName, "namespace", namespace)
	return nil
}

// RetryWorkflow 重试工作流
func (m *ArgoWorkflowManager) RetryWorkflow(ctx context.Context, clusterID, namespace, workflowName, stepName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取工作流失败: %w", err)
	}

	// 重置指定步骤的状态
	annotations := workflow.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations["workflows.argoproj.io/retry-step"] = stepName

	workflow.SetAnnotations(annotations)

	// 更新工作流
	_, err = dynamicClient.Resource(workflowResource).Namespace(namespace).Update(ctx, workflow, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新工作流失败: %w", err)
	}

	logger.Info("工作流步骤重试成功", "cluster_id", clusterID, "workflow_name", workflowName, "step_name", stepName)
	return nil
}

// getRestConfig 获取 REST 配置
func (m *ArgoWorkflowManager) getRestConfig(ctx context.Context, kceClusterID string) (*rest.Config, error) {
	kubeConfigBytes, err := utils.GetKubernetesConfig(kceClusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 kubeconfig 失败: %w", err)
	}

	config, err := utils.BuildConfigFromBytes(kubeConfigBytes)
	if err != nil {
		return nil, fmt.Errorf("构建 REST 配置失败: %w", err)
	}

	return config, nil
}

// ensureArgoNamespace 确保 Argo 命名空间存在
func (m *ArgoWorkflowManager) ensureArgoNamespace(ctx context.Context, client *kubernetes.Clientset, namespace string) error {
	// 确保命名空间存在
	_, err := client.CoreV1().Namespaces().Get(ctx, namespace, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建命名空间
			ns := &corev1.Namespace{
				ObjectMeta: metav1.ObjectMeta{
					Name: namespace,
				},
			}
			_, err = client.CoreV1().Namespaces().Create(ctx, ns, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建命名空间失败: %w", err)
			}
			logger.Info("创建 Argo 命名空间成功", "namespace", namespace)
		} else {
			return fmt.Errorf("获取命名空间失败: %w", err)
		}
	}
	return nil
}
