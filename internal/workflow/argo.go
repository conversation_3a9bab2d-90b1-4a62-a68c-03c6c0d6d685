package workflow

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/kingsoft/nimbus/pkg/logger"
	"github.com/kingsoft/nimbus/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

const (
	// ArgoWorkflowGroup Argo Workflow 的 API Group
	ArgoWorkflowGroup = "argoproj.io"
	// ArgoWorkflowVersion Argo Workflow 的 API Version
	ArgoWorkflowVersion = "v1alpha1"
	// ArgoWorkflowResource Argo Workflow 的资源名
	ArgoWorkflowResource = "workflows"
	// ArgoWorkflowNamespace Argo Workflow 的默认命名空间
	ArgoWorkflowNamespace = "argo"
)

// kubectlInClusterSetupShell 在容器内使用 ServiceAccount 自动生成 kubeconfig 的脚本
// 解决某些镜像或环境下 kubectl 退回到 http://localhost:8080 导致连接失败的问题
const kubectlInClusterSetupShell = `
# 尝试基于容器内的 ServiceAccount 自动配置 kubectl 访问当前集群
if [ -n "$KUBERNETES_SERVICE_HOST" ] && [ -f "/var/run/secrets/kubernetes.io/serviceaccount/token" ]; then
  echo "[setup] 配置 kubectl 使用 in-cluster ServiceAccount 凭证"
  export K8S_HOST="https://${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT:-443}"
  # 生成临时 kubeconfig 文件
  cat >/tmp/kubeconfig <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    certificate-authority: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    server: ${K8S_HOST}
  name: in-cluster
contexts:
- context:
    cluster: in-cluster
    user: in-cluster-user
  name: in-cluster
current-context: in-cluster
users:
- name: in-cluster-user
  user:
    token: $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
EOF
  export KUBECONFIG=/tmp/kubeconfig
fi
`

// ArgoWorkflowManager Argo Workflow 管理器
type ArgoWorkflowManager struct {
	clientManager *innerKubernetes.ClientManager
}

// NewArgoWorkflowManager 创建 Argo Workflow 管理器
func NewArgoWorkflowManager(clientManager *innerKubernetes.ClientManager) *ArgoWorkflowManager {
	return &ArgoWorkflowManager{
		clientManager: clientManager,
	}
}

// WorkflowDefinition 工作流定义结构
type WorkflowDefinition struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Labels      map[string]string `json:"labels,omitempty"`
	Spec        WorkflowSpec      `json:"spec"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// WorkflowSpec 工作流规格
type WorkflowSpec struct {
	Entrypoint         string             `json:"entrypoint"`
	Templates          []WorkflowTemplate `json:"templates"`
	Arguments          *WorkflowInputs    `json:"arguments,omitempty"`
	ServiceAccountName string             `json:"serviceAccountName,omitempty"`
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	Name      string                 `json:"name"`
	Steps     [][]WorkflowStep       `json:"steps,omitempty"`
	Container *WorkflowContainerSpec `json:"container,omitempty"`
	Script    *WorkflowScriptSpec    `json:"script,omitempty"`
	Inputs    *WorkflowInputs        `json:"inputs,omitempty"`
	Outputs   *WorkflowOutputs       `json:"outputs,omitempty"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	Name      string          `json:"name"`
	Template  string          `json:"template"`
	Arguments *WorkflowInputs `json:"arguments,omitempty"`
}

// WorkflowContainerSpec 容器规格
type WorkflowContainerSpec struct {
	Image   string   `json:"image"`
	Command []string `json:"command,omitempty"`
	Args    []string `json:"args,omitempty"`
	Env     []EnvVar `json:"env,omitempty"`
}

// WorkflowScriptSpec 脚本规格
type WorkflowScriptSpec struct {
	Image   string   `json:"image"`
	Source  string   `json:"source"`
	Command []string `json:"command,omitempty"`
}

// WorkflowInputs 工作流输入
type WorkflowInputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowOutputs 工作流输出
type WorkflowOutputs struct {
	Parameters []WorkflowParameter `json:"parameters,omitempty"`
}

// WorkflowParameter 工作流参数
type WorkflowParameter struct {
	Name  string `json:"name"`
	Value string `json:"value,omitempty"`
}

// EnvVar 环境变量
type EnvVar struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// SubmitWorkflow 提交工作流到 Argo
func (m *ArgoWorkflowManager) SubmitWorkflow(ctx context.Context, kceClusterID string, workflowDef WorkflowDefinition) (string, error) {
	// 获取 Kubernetes 客户端
	client, err := m.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 创建动态客户端
	config, err := m.getRestConfig(ctx, kceClusterID)
	if err != nil {
		return "", fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 确保 Argo 命名空间存在
	if err := m.ensureArgoNamespace(ctx, client, kceClusterID, workflowDef.Namespace); err != nil {
		return "", fmt.Errorf("确保 Argo 命名空间失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 生成唯一的工作流名称
	if workflowDef.Name == "" {
		workflowDef.Name = fmt.Sprintf("wf-%s", uuid.New().String()[:8])
	}

	// 设置默认命名空间
	if workflowDef.Namespace == "" {
		workflowDef.Namespace = ArgoWorkflowNamespace
	}

	// 构建 Unstructured 对象
	workflow := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": fmt.Sprintf("%s/%s", ArgoWorkflowGroup, ArgoWorkflowVersion),
			"kind":       "Workflow",
			"metadata": map[string]interface{}{
				"name":        workflowDef.Name,
				"namespace":   workflowDef.Namespace,
				"labels":      workflowDef.Labels,
				"annotations": workflowDef.Annotations,
			},
			"spec": workflowDef.Spec,
		},
	}

	// 提交工作流
	result, err := dynamicClient.Resource(workflowResource).Namespace(workflowDef.Namespace).Create(ctx, workflow, metav1.CreateOptions{})
	if err != nil {
		return "", fmt.Errorf("创建工作流失败: %w", err)
	}

	workflowName := result.GetName()
	logger.Info("Argo 工作流提交成功", "cluster_id", kceClusterID, "workflow_name", workflowName, "namespace", workflowDef.Namespace)

	return workflowName, nil
}

// GetWorkflowStatus 获取工作流状态
func (m *ArgoWorkflowManager) GetWorkflowStatus(ctx context.Context, clusterID, namespace, workflowName string) (string, error) {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return "", fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return "", fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "NotFound", nil
		}
		return "", fmt.Errorf("获取工作流失败: %w", err)
	}

	// 提取状态
	status, found, err := unstructured.NestedString(workflow.Object, "status", "phase")
	if err != nil {
		return "", fmt.Errorf("提取工作流状态失败: %w", err)
	}

	if !found {
		return "Pending", nil
	}

	return status, nil
}

// CancelWorkflow 取消工作流
func (m *ArgoWorkflowManager) CancelWorkflow(ctx context.Context, clusterID, namespace, workflowName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 删除工作流
	err = dynamicClient.Resource(workflowResource).Namespace(namespace).Delete(ctx, workflowName, metav1.DeleteOptions{})
	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("删除工作流失败: %w", err)
	}

	logger.Info("Argo 工作流取消成功", "cluster_id", clusterID, "workflow_name", workflowName, "namespace", namespace)
	return nil
}

// CreateClusterWorkflow 创建集群管理工作流
func (m *ArgoWorkflowManager) CreateClusterWorkflow(operationType, clusterID, clusterName, kceClusterID string, services []string) WorkflowDefinition {
	workflowName := fmt.Sprintf("%s-cluster-%s-%s", operationType, clusterName, uuid.New().String()[:8])

	switch operationType {
	case "create":
		return m.createClusterCreationWorkflow(workflowName, clusterID, clusterName, kceClusterID, services)
	case "delete":
		return m.createClusterDeletionWorkflow(workflowName, clusterID, clusterName, kceClusterID)
	default:
		logger.Error("不支持的集群操作类型", "operation_type", operationType)
		return WorkflowDefinition{}
	}
}

// createClusterCreationWorkflow 创建集群创建工作流
func (m *ArgoWorkflowManager) createClusterCreationWorkflow(workflowName, clusterID, clusterName, kceClusterID string, services []string) WorkflowDefinition {

	// 构建服务安装步骤
	serviceSteps := make([]WorkflowStep, 0, len(services))
	for _, service := range services {
		serviceSteps = append(serviceSteps, WorkflowStep{
			Name:     fmt.Sprintf("install-%s-operator", service),
			Template: fmt.Sprintf("install-%s-operator-template", service),
			Arguments: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
					{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
					{Name: "service-type", Value: service},
				},
			},
		})
	}

	templates := []WorkflowTemplate{
		{
			Name: "cluster-creation-main",
			Steps: [][]WorkflowStep{
				// 第1步：验证集群连接
				{
					{
						Name:     "validate-cluster",
						Template: "validate-cluster-connection",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "kce-cluster-id", Value: "{{workflow.parameters.kce-cluster-id}}"},
							},
						},
					},
				},
				// 第2步：创建基础资源
				{
					{
						Name:     "create-base-resources",
						Template: "create-base-resources",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
								{Name: "cluster-name", Value: "{{workflow.parameters.cluster-name}}"},
							},
						},
					},
				},
				// 第3步：并行安装服务（如果有的话）
				serviceSteps,
				// 第4步：验证部署状态
				{
					{
						Name:     "verify-deployments",
						Template: "verify-all-deployments",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "services", Value: "{{workflow.parameters.services}}"},
							},
						},
					},
				},
				// 第5步：更新集群状态
				{
					{
						Name:     "update-cluster-status",
						Template: "update-cluster-status",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "cluster-id", Value: "{{workflow.parameters.cluster-id}}"},
								{Name: "status", Value: "active"},
								{Name: "services", Value: "{{workflow.parameters.services}}"},
							},
						},
					},
				},
			},
		},
		// 验证集群连接模板
		{
			Name: "validate-cluster-connection",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始验证KCE集群连接: {{inputs.parameters.kce-cluster-id}}"

# 验证kubectl可以连接到集群
echo "检查集群基本信息..."
kubectl cluster-info --request-timeout=30s

echo "检查集群节点状态..."
kubectl get nodes --no-headers | head -5

echo "检查集群版本信息..."
kubectl version

echo "验证集群命名空间..."
kubectl get namespaces

echo "✓ KCE集群 {{inputs.parameters.kce-cluster-id}} 连接验证成功"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "kce-cluster-id"},
				},
			},
		},
		// 创建基础资源模板
		{
			Name: "create-base-resources",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Creating base resources for cluster {{inputs.parameters.cluster-name}}"

# 创建必要的命名空间
kubectl create namespace nimbus-system --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace kmrspark --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace kmrflink --dry-run=client -o yaml | kubectl apply -f -

# 创建基础 ConfigMap
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-info
  namespace: nimbus-system
data:
  cluster-id: "{{inputs.parameters.cluster-id}}"
  cluster-name: "{{inputs.parameters.cluster-name}}"
  created-at: "$(date -Iseconds)"
EOF

echo "Base resources created successfully"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "cluster-name"},
				},
			},
		},
		// 验证所有部署模板
		{
			Name: "verify-all-deployments",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

services="{{inputs.parameters.services}}"
echo "验证部署状态，服务列表: $services"

# 检查 Spark Operator
if echo "$services" | grep -q "spark"; then
    echo "验证 Spark Operator 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
    echo "✓ Spark Operator 验证成功"
    
    # 检查Spark History是否安装
    if kubectl get deployment spark-history -n kmrspark >/dev/null 2>&1; then
        echo "验证 Spark History 部署状态..."
        kubectl wait --for=condition=available --timeout=300s deployment/spark-history -n kmrspark
        echo "✓ Spark History 验证成功"
    else
        echo "⚠ Spark History 未安装，跳过验证"
    fi
fi

# 检查 Flink Operator
if echo "$services" | grep -q "flink"; then
    echo "验证 Flink Operator 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/flink-kubernetes-operator -n kmrflink
    echo "✓ Flink Operator 验证成功"
fi

echo "✓ 所有部署验证完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "services"},
				},
			},
		},
		// 更新集群状态模板
		{
			Name: "update-cluster-status",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "更新集群状态为 active..."

# 尝试调用API更新集群状态（如果nimbus服务在集群内运行）
echo "尝试调用nimbus API更新集群状态..."
if curl -f --connect-timeout 10 --max-time 30 -X PUT "http://nimbus-api-service:8080/api/v1/clusters/{{inputs.parameters.cluster-id}}" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "services": "{{inputs.parameters.services}}"
  }' > /dev/null 2>&1; then
    echo "✓ 集群状态通过API更新成功"
else
    echo "⚠ 无法访问nimbus API服务（可能运行在集群外），跳过API更新"
    echo "这是正常情况，如果nimbus服务运行在本地"
fi

# 同时创建集群状态 ConfigMap 作为备份
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-status
  namespace: nimbus-system
data:
  status: "active"
  updated-at: "$(date -Iseconds)"
  services: "{{inputs.parameters.services}}"
EOF

echo "集群状态更新完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "services"},
				},
			},
		},
	}

	// 添加服务安装模板
	if len(services) > 0 {
		for _, service := range services {
			switch service {
			case "spark":
				// 使用简化的Spark工作流模板
				templates = append(templates, m.getSparkOperatorInstallTemplates()...)
			case "flink":
				templates = append(templates, m.getFlinkOperatorInstallTemplates()...)
			}
		}
	}

	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":          "nimbus",
			"operation":    "cluster-create",
			"cluster-id":   clusterID,
			"cluster-name": clusterName,
		},
		Annotations: map[string]string{
			"nimbus.io/cluster-id":     clusterID,
			"nimbus.io/kce-cluster-id": kceClusterID,
			"nimbus.io/services":       fmt.Sprintf("[%s]", strings.Join(services, ",")),
		},
		Spec: WorkflowSpec{
			Entrypoint:         "cluster-creation-main",
			ServiceAccountName: "kubectl-admin-sa",
			Templates:          templates,
			Arguments: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id", Value: clusterID},
					{Name: "cluster-name", Value: clusterName},
					{Name: "kce-cluster-id", Value: kceClusterID},
					{Name: "services", Value: fmt.Sprintf("[%s]", strings.Join(services, ","))},
				},
			},
		},
	}
}

// createClusterDeletionWorkflow 创建集群删除工作流
func (m *ArgoWorkflowManager) createClusterDeletionWorkflow(workflowName, clusterID, clusterName, kceClusterID string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operation":  "cluster-delete",
			"cluster-id": clusterID,
		},
		Annotations: map[string]string{
			"nimbus.io/cluster-id":     clusterID,
			"nimbus.io/kce-cluster-id": kceClusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "cluster-deletion-main",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "cluster-deletion-main",
					Steps: [][]WorkflowStep{
						// 第1步：获取集群信息
						{
							{
								Name:     "get-cluster-info",
								Template: "get-cluster-info",
								Arguments: &WorkflowInputs{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: clusterID},
									},
								},
							},
						},
						// 第2步：卸载 Operators
						{
							{
								Name:     "uninstall-spark-operator",
								Template: "uninstall-spark-operator",
							},
							{
								Name:     "uninstall-flink-operator",
								Template: "uninstall-flink-operator",
							},
						},
						// 第3步：清理资源
						{
							{
								Name:     "cleanup-resources",
								Template: "cleanup-cluster-resources",
								Arguments: &WorkflowInputs{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: clusterID},
									},
								},
							},
						},
						// 第4步：更新集群状态
						{
							{
								Name:     "mark-cluster-deleted",
								Template: "mark-cluster-deleted",
								Arguments: &WorkflowInputs{
									Parameters: []WorkflowParameter{
										{Name: "cluster-id", Value: clusterID},
									},
								},
							},
						},
					},
				},
				// 获取集群信息模板
				{
					Name: "get-cluster-info",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
# 注意：不使用 set -e，以便在API访问失败时继续执行

echo "获取集群信息..."
echo "集群ID: {{inputs.parameters.cluster-id}}"

# 设置默认服务列表
services="spark,flink"
echo "默认服务列表: $services"

# 尝试获取集群信息（如果nimbus服务在集群内运行）
echo "尝试从nimbus API获取集群信息..."
api_success=false

if command -v curl > /dev/null 2>&1; then
    echo "curl命令可用，尝试API调用..."

    # 使用更短的超时时间，避免长时间卡住
    if response=$(curl -f --connect-timeout 5 --max-time 10 -s "http://nimbus-api-service:8080/api/v1/clusters/{{inputs.parameters.cluster-id}}" 2>/dev/null); then
        if [ -n "$response" ] && [ "$response" != "" ] && [ "$response" != "null" ]; then
            echo "✓ 成功获取集群信息"
            api_success=true

            # 尝试解析服务列表（不依赖jq）
            if command -v jq > /dev/null 2>&1; then
                echo "使用jq解析服务列表..."
                parsed_services=$(echo "$response" | jq -r '.services // "spark,flink"' 2>/dev/null || echo "spark,flink")
                if [ -n "$parsed_services" ] && [ "$parsed_services" != "null" ]; then
                    services="$parsed_services"
                fi
            else
                echo "jq命令不可用，使用默认服务列表"
                # 简单的字符串匹配（不依赖jq）
                if echo "$response" | grep -q '"services"'; then
                    echo "检测到服务配置，使用默认列表"
                fi
            fi
        fi
    fi
fi

if [ "$api_success" = "false" ]; then
    echo "⚠ 无法访问nimbus API服务（可能运行在集群外或网络问题）"
    echo "这是正常情况，如果nimbus服务运行在本地"
    echo "使用默认服务列表进行清理"
fi

echo "最终服务列表: $services"

# 存储到输出参数
echo "$services" > /tmp/services.txt
echo "✓ 集群信息获取完成，将清理服务: $services"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
						},
					},
					Outputs: &WorkflowOutputs{
						Parameters: []WorkflowParameter{
							{Name: "services", Value: "$(cat /tmp/services.txt)"},
						},
					},
				},
				// 卸载 Spark Operator 模板
				{
					Name: "uninstall-spark-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Uninstalling Spark Operator..."

# 检查 Spark Operator 是否存在
if kubectl get namespace kmrspark >/dev/null 2>&1; then
    echo "Found Spark Operator, proceeding with uninstallation..."
    
    # 删除 Spark Operator 部署
    kubectl delete deployment spark-operator -n kmrspark --ignore-not-found=true
    
    # 删除 RBAC 资源
    kubectl delete clusterrolebinding spark-operator --ignore-not-found=true
    kubectl delete clusterrole spark-operator --ignore-not-found=true
    kubectl delete serviceaccount spark-operator -n kmrspark --ignore-not-found=true
    
    # 删除命名空间
    kubectl delete namespace kmrspark --ignore-not-found=true
    
    echo "Spark Operator uninstalled successfully"
else
    echo "Spark Operator not found, skipping uninstallation"
fi
`,
					},
				},
				// 卸载 Flink Operator 模板
				{
					Name: "uninstall-flink-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "Uninstalling Flink Operator..."

# 检查 Flink Operator 是否存在
if kubectl get namespace kmrflink >/dev/null 2>&1; then
    echo "Found Flink Operator, proceeding with uninstallation..."
    
    # 删除 Flink Operator 部署
    kubectl delete deployment flink-kubernetes-operator -n kmrflink --ignore-not-found=true
    
    # 删除 RBAC 资源
    kubectl delete clusterrolebinding flink-operator --ignore-not-found=true
    kubectl delete clusterrole flink-operator --ignore-not-found=true
    kubectl delete serviceaccount flink-operator -n kmrflink --ignore-not-found=true
    
    # 删除命名空间
    kubectl delete namespace kmrflink --ignore-not-found=true
    
    echo "Flink Operator uninstalled successfully"
else
    echo "Flink Operator not found, skipping uninstallation"
fi
`,
					},
				},
				// 清理集群资源模板
				{
					Name: "cleanup-cluster-resources",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "清理集群相关资源..."
echo "集群ID: {{inputs.parameters.cluster-id}}"

# 只删除与特定集群相关的命名空间，不删除系统命名空间
echo "删除集群相关的工作负载命名空间..."
kubectl delete namespace kmrspark --ignore-not-found=true
kubectl delete namespace kmrflink --ignore-not-found=true

# 清理集群特定的资源（使用集群ID标签）
echo "清理集群特定的资源..."
CLUSTER_ID="{{inputs.parameters.cluster-id}}"
if [ -n "$CLUSTER_ID" ]; then
    # 清理带有集群ID标签的资源
    kubectl delete all -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true
    kubectl delete configmap -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true
    kubectl delete secret -l cluster-id="$CLUSTER_ID" --all-namespaces --ignore-not-found=true

    echo "✓ 已清理集群 $CLUSTER_ID 的相关资源"
else
    echo "⚠ 集群ID为空，跳过资源清理"
fi

# 注意：不删除 nimbus-system 命名空间，因为它包含系统服务
echo "⚠ 保留 nimbus-system 命名空间（包含系统服务）"

echo "✓ 集群资源清理完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
						},
					},
				},
				// 标记集群已删除模板
				{
					Name: "mark-cluster-deleted",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "标记集群为已删除状态..."

# 尝试调用内部API删除集群记录（如果nimbus服务在集群内运行）
echo "尝试调用nimbus API删除集群记录..."
if curl -f --connect-timeout 10 --max-time 30 -X DELETE "http://nimbus-api-service:8080/api/v1/clusters/{{inputs.parameters.cluster-id}}" \
  -H "Content-Type: application/json" > /dev/null 2>&1; then
    echo "✓ 集群记录通过API删除成功"
else
    echo "⚠ 无法访问nimbus API服务（可能运行在集群外），跳过API删除"
    echo "这是正常情况，如果nimbus服务运行在本地"
fi

# 创建集群删除状态的ConfigMap作为备份记录
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-deletion-record
  namespace: nimbus-system
data:
  cluster-id: "{{inputs.parameters.cluster-id}}"
  status: "deleted"
  deleted-at: "$(date -Iseconds)"
EOF

echo "✓ 集群删除状态标记完成"
`,
					},
					Inputs: &WorkflowInputs{
						Parameters: []WorkflowParameter{
							{Name: "cluster-id"},
						},
					},
				},
			},
		},
	}
}

// createSparkOperatorWorkflow 创建 Spark Operator 安装工作流
func (m *ArgoWorkflowManager) createSparkOperatorWorkflow(workflowName, clusterID string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operator":   "spark",
			"cluster-id": clusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "install-spark-operator",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "install-spark-operator",
					Steps: [][]WorkflowStep{
						{
							{
								Name:     "create-namespace",
								Template: "create-spark-namespace",
							},
						},
						{
							{
								Name:     "install-crds",
								Template: "install-spark-crds",
							},
						},
						{
							{
								Name:     "install-operator",
								Template: "install-spark-operator-helm",
							},
						},
						{
							{
								Name:     "verify-deployment",
								Template: "verify-spark-deployment",
							},
						},
					},
				},
				{
					Name: "install-spark-crds",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "安装 Spark Operator CRDs..."

# 直接应用 CRDs 定义
kubectl apply -f - <<EOF
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: sparkapplications.sparkoperator.k8s.io
spec:
  group: sparkoperator.k8s.io
  names:
    kind: SparkApplication
    listKind: SparkApplicationList
    plural: sparkapplications
    singular: sparkapplication
  scope: Namespaced
  versions:
  - name: v1beta2
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              type:
                type: string
              mode:
                type: string
              image:
                type: string
              sparkVersion:
                type: string
              restartPolicy:
                type: object
                properties:
                  type:
                    type: string
              driver:
                type: object
                properties:
                  cores:
                    type: string
                  memory:
                    type: string
                  serviceAccount:
                    type: string
              executor:
                type: object
                properties:
                  cores:
                    type: string
                  instances:
                    type: integer
                  memory:
                    type: string
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: scheduledsparkapplications.sparkoperator.k8s.io
spec:
  group: sparkoperator.k8s.io
  names:
    kind: ScheduledSparkApplication
    listKind: ScheduledSparkApplicationList
    plural: scheduledsparkapplications
    singular: scheduledsparkapplication
  scope: Namespaced
  versions:
  - name: v1beta2
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              schedule:
                type: string
              template:
                type: object
                properties:
                  spec:
                    type: object
    subresources:
      status: {}
EOF

echo "Spark Operator CRDs 安装完成"
`,
					},
				},
				{
					Name: "create-spark-namespace",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "创建 kmrspark 命名空间..."
kubectl create namespace kmrspark --dry-run=client -o yaml | kubectl apply -f -
echo "Spark Operator 命名空间 kmrspark 创建成功"
`,
					},
				},
				{
					Name: "install-spark-operator-helm",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Spark Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall spark-operator -n kmrspark || true
kubectl delete namespace kmrspark || true

# 全新安装Spark Operator
echo "全新安装Spark Operator..."
helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --create-namespace \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --wait

echo "✓ Spark Operator安装完成"
`,
					},
				},
				{
					Name: "verify-spark-deployment",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "验证 Spark Operator 部署状态..."
kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
echo "检查 Spark Operator pods 状态..."
kubectl get pods -n kmrspark
echo "检查 Spark Operator 服务状态..."
kubectl get services -n kmrspark
echo "Spark Operator 部署验证成功"
`,
					},
				},
			},
		},
	}
}

// createFlinkOperatorWorkflow 创建 Flink Operator 安装工作流
func (m *ArgoWorkflowManager) createFlinkOperatorWorkflow(workflowName, clusterID string) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"operator":   "flink",
			"cluster-id": clusterID,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "install-flink-operator",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "install-flink-operator",
					Steps: [][]WorkflowStep{
						{
							{
								Name:     "create-namespace",
								Template: "create-flink-namespace",
							},
						},
						{
							{
								Name:     "create-rbac",
								Template: "create-flink-rbac",
							},
						},
						{
							{
								Name:     "deploy-operator",
								Template: "deploy-flink-operator",
							},
						},
						{
							{
								Name:     "verify-deployment",
								Template: "verify-flink-deployment",
							},
						},
					},
				},
				{
					Name: "create-flink-rbac",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

kubectl apply -f - <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: flink-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "persistentvolumeclaims", "events", "configmaps", "secrets", "serviceaccounts"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments", "deployments/finalizers", "replicasets"]
  verbs: ["*"]
- apiGroups: ["flink.apache.org"]
  resources: ["flinkdeployments", "flinkdeployments/status", "flinksessionjobs", "flinksessionjobs/status"]
  verbs: ["*"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["*"]
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: flink-operator
subjects:
- kind: ServiceAccount
  name: flink-operator
  namespace: kmrflink
roleRef:
  kind: ClusterRole
  name: flink-operator
  apiGroup: rbac.authorization.k8s.io
EOF`,
					},
				},
				{
					Name: "deploy-flink-operator",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flink-kubernetes-operator
  namespace: kmrflink
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: flink-kubernetes-operator
  template:
    metadata:
      labels:
        app.kubernetes.io/name: flink-kubernetes-operator
    spec:
      serviceAccountName: flink-operator
      containers:
      - name: flink-kubernetes-operator
        image: ghcr.io/apache/flink-kubernetes-operator:1.6.1
        ports:
        - containerPort: 9999
          name: metrics
        - containerPort: 9443
          name: webhook
        env:
        - name: WATCH_NAMESPACE
          value: ""
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: OPERATOR_NAME
          value: "flink-kubernetes-operator"
EOF`,
					},
				},
				{
					Name: "verify-flink-deployment",
					Container: &WorkflowContainerSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"kubectl"},
						Args: []string{
							"wait", "--for=condition=available",
							"--timeout=300s",
							"deployment/flink-kubernetes-operator",
							"-n", "kmrflink",
						},
					},
				},
			},
		},
	}
}

// getRestConfig 获取 REST 配置
func (m *ArgoWorkflowManager) getRestConfig(ctx context.Context, clusterID string) (*rest.Config, error) {
	// 通过客户端管理器获取集群配置
	client, err := m.clientManager.GetClient(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 获取客户端配置
	config, err := m.clientManager.GetConfig(ctx, clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取集群配置失败: %w", err)
	}

	// 使用客户端确保连接可用
	_, err = client.Discovery().ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("验证集群连接失败: %w", err)
	}

	return config, nil
}

// ensureArgoNamespace 确保 Argo 命名空间存在，并安装 Argo Workflows（如果未安装）
func (m *ArgoWorkflowManager) ensureArgoNamespace(ctx context.Context, client *kubernetes.Clientset, kceClusterID string, namespace string) error {
	// 检查 Argo Workflows 是否已安装
	installed, err := m.isArgoWorkflowsInstalled(ctx, client, namespace)
	if err != nil {
		return fmt.Errorf("检查 Argo Workflows 安装状态失败: %w", err)
	}

	if !installed {
		// Argo Workflows 未安装，使用本地 Helm Chart 安装
		logger.Info("Argo Workflows 未安装，开始安装", "namespace", namespace)
		if err := m.installArgoWorkflowsFromLocal(ctx, kceClusterID, namespace); err != nil {
			return fmt.Errorf("安装 Argo Workflows 失败: %w", err)
		}
		logger.Info("Argo Workflows 安装成功", "namespace", namespace)
	} else {
		logger.Info("Argo Workflows 已安装", "namespace", namespace)
	}

	// 确保命名空间存在
	_, err = client.CoreV1().Namespaces().Get(ctx, namespace, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 创建命名空间
			ns := &corev1.Namespace{
				ObjectMeta: metav1.ObjectMeta{
					Name: namespace,
				},
			}
			_, err = client.CoreV1().Namespaces().Create(ctx, ns, metav1.CreateOptions{})
			if err != nil {
				return fmt.Errorf("创建命名空间失败: %w", err)
			}
			logger.Info("创建 Argo 命名空间成功", "namespace", namespace)
		} else {
			return fmt.Errorf("获取命名空间失败: %w", err)
		}
	}
	return nil
}

// getSparkOperatorInstallTemplates 获取 Spark Operator 安装模板集合
func (m *ArgoWorkflowManager) getSparkOperatorInstallTemplates() []WorkflowTemplate {
	return []WorkflowTemplate{
		{
			Name: "install-spark-operator-template",
			Steps: [][]WorkflowStep{
				{
					{
						Name:     "install-spark-operator",
						Template: "install-spark-operator-helm",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "kce-cluster-id", Value: "{{inputs.parameters.kce-cluster-id}}"},
								{Name: "cluster-id", Value: "{{inputs.parameters.cluster-id}}"},
							},
						},
					},
				},
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "kce-cluster-id"},
					{Name: "service-type"},
				},
			},
		},

		{
			Name: "install-spark-operator-helm",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装Spark Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 彻底清理可能存在的旧资源
echo "彻底清理可能存在的旧资源..."

# 1. 先尝试卸载 Helm release
helm uninstall spark-operator -n kmrspark || true
sleep 5

# 2. 强制删除可能残留的资源
echo "强制删除残留资源..."
kubectl delete serviceaccount spark-operator -n kmrspark --ignore-not-found=true
kubectl delete clusterrole spark-operator --ignore-not-found=true
kubectl delete clusterrolebinding spark-operator --ignore-not-found=true
kubectl delete role spark-operator -n kmrspark --ignore-not-found=true
kubectl delete rolebinding spark-operator -n kmrspark --ignore-not-found=true
kubectl delete deployment spark-operator -n kmrspark --ignore-not-found=true
kubectl delete service spark-operator -n kmrspark --ignore-not-found=true

# 3. 删除并重新创建命名空间
kubectl delete namespace kmrspark --ignore-not-found=true
sleep 10
kubectl create namespace kmrspark

# 4. 全新安装Spark Operator
echo "全新安装Spark Operator..."
helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --wait --timeout=10m
echo "✓ Spark Operator安装完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "kce-cluster-id"},
					{Name: "cluster-id"},
				},
			},
		},
	}
}

// getFlinkOperatorInstallTemplates 获取 Flink Operator 安装模板集合
func (m *ArgoWorkflowManager) getFlinkOperatorInstallTemplates() []WorkflowTemplate {
	return []WorkflowTemplate{
		{
			Name: "install-flink-operator-template",
			Steps: [][]WorkflowStep{
				{
					{
						Name:     "deploy-flink-operator",
						Template: "deploy-flink-operator",
						Arguments: &WorkflowInputs{
							Parameters: []WorkflowParameter{
								{Name: "kce-cluster-id", Value: "{{inputs.parameters.kce-cluster-id}}"},
							},
						},
					},
				},
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "cluster-id"},
					{Name: "kce-cluster-id"},
					{Name: "service-type"},
				},
			},
		},

		{
			Name: "deploy-flink-operator",
			Script: &WorkflowScriptSpec{
				Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
				Command: []string{"/bin/sh"},
				Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "部署 Flink Operator (KCE集群: {{inputs.parameters.kce-cluster-id}})..."

# 使用Helm仓库安装Flink Operator
echo "使用Helm仓库安装Flink Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall flink-operator -n kmrflink || true
kubectl delete namespace kmrflink || true

# 全新安装Flink Operator
echo "全新安装Flink Operator..."
helm install flink-operator kmr-on-kce/flink-operator \
  --namespace kmrflink \
  --create-namespace \
  --set image.repository=ghcr.io/apache/flink-kubernetes-operator \
  --set image.tag=1.6.1 \
  --wait --timeout=10m

echo "✓ Flink Operator安装完成"
`,
			},
			Inputs: &WorkflowInputs{
				Parameters: []WorkflowParameter{
					{Name: "kce-cluster-id"},
				},
			},
		},
	}
}

// CreateOperatorInstallWorkflow 创建 Operator 安装工作流
func (m *ArgoWorkflowManager) CreateOperatorInstallWorkflow(operatorType, clusterID string) WorkflowDefinition {
	workflowName := fmt.Sprintf("install-%s-operator-%s", operatorType, uuid.New().String()[:8])

	switch operatorType {
	case "spark":
		return m.createSparkOperatorWorkflow(workflowName, clusterID)
	case "flink":
		return m.createFlinkOperatorWorkflow(workflowName, clusterID)
	default:
		logger.Error("不支持的 Operator 类型", "operator_type", operatorType)
		return WorkflowDefinition{}
	}
}

// RetryWorkflowStep 重试工作流步骤
func (m *ArgoWorkflowManager) RetryWorkflowStep(ctx context.Context, clusterID, namespace, workflowName, stepName string) error {
	// 获取 REST 配置
	config, err := m.getRestConfig(ctx, clusterID)
	if err != nil {
		return fmt.Errorf("获取 REST 配置失败: %w", err)
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("创建动态客户端失败: %w", err)
	}

	// 构建 workflow 资源
	workflowResource := schema.GroupVersionResource{
		Group:    ArgoWorkflowGroup,
		Version:  ArgoWorkflowVersion,
		Resource: ArgoWorkflowResource,
	}

	// 获取当前工作流
	workflow, err := dynamicClient.Resource(workflowResource).Namespace(namespace).Get(ctx, workflowName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("获取工作流失败: %w", err)
	}

	// 重置指定步骤的状态
	// 这里是重试逻辑的简化版本，实际实现需要更复杂的状态管理
	annotations := workflow.GetAnnotations()
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations["workflows.argoproj.io/retry-step"] = stepName

	workflow.SetAnnotations(annotations)

	// 更新工作流
	_, err = dynamicClient.Resource(workflowResource).Namespace(namespace).Update(ctx, workflow, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("更新工作流失败: %w", err)
	}

	logger.Info("工作流步骤重试成功", "cluster_id", clusterID, "workflow_name", workflowName, "step_name", stepName)
	return nil
}

// isArgoWorkflowsInstalled 检查 Argo Workflows 是否已安装
func (m *ArgoWorkflowManager) isArgoWorkflowsInstalled(ctx context.Context, client *kubernetes.Clientset, namespace string) (bool, error) {
	// 检查 workflow-controller deployment 是否存在
	_, err := client.AppsV1().Deployments(namespace).Get(ctx, "argo-workflows-workflow-controller", metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("检查 workflow-controller deployment 失败: %w", err)
	}

	// 检查 argo-server deployment 是否存在（可选组件）
	_, err = client.AppsV1().Deployments(namespace).Get(ctx, "argo-server", metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			logger.Info("Argo Server 未安装，仅检查到 workflow-controller", "namespace", namespace)
		} else {
			logger.Warn("检查 argo-server deployment 失败", "error", err.Error())
		}
	}

	return true, nil
}

// installArgoWorkflowsFromLocal 使用本地 Helm Chart 安装 Argo Workflows
func (m *ArgoWorkflowManager) installArgoWorkflowsFromLocal(ctx context.Context, kceClusterID string, namespace string) error {
	// 使用默认集群配置或通过环境变量获取 kubeconfig
	// 这里我们假设使用默认的集群配置
	kubeConfigBytes, err := utils.GetKubernetesConfig(kceClusterID)
	if err != nil {
		return fmt.Errorf("获取 kubeconfig 失败: %w", err)
	}

	// 使用本地 Helm Chart 路径
	chartPath := "./deploy/helm/argo-workflows"
	releaseName := "argo-workflows"
	version := "1.0.0"

	// 为不同的集群生成不同的 ingress 配置
	values := m.generateArgoWorkflowsValues(kceClusterID)

	// 调用 InstallHelmChartFromLocal 安装 Argo Workflows
	err = innerKubernetes.InstallHelmChartFromLocal(kubeConfigBytes, namespace, chartPath, releaseName, version, values)
	if err != nil {
		return fmt.Errorf("使用 Helm 安装 Argo Workflows 失败: %w", err)
	}

	// 等待 deployment 就绪
	return m.waitForArgoWorkflowsReady(ctx, kceClusterID, namespace)
}

// waitForArgoWorkflowsReady 等待 Argo Workflows 部署就绪
func (m *ArgoWorkflowManager) waitForArgoWorkflowsReady(ctx context.Context, kceClusterID string, namespace string) error {
	logger.Info("等待 Argo Workflows 部署就绪", "namespace", namespace)

	// 获取 Kubernetes 客户端
	client, err := m.clientManager.GetClient(ctx, kceClusterID)
	if err != nil {
		return fmt.Errorf("获取 Kubernetes 客户端失败: %w", err)
	}

	// 等待 workflow-controller 就绪
	for i := 0; i < 30; i++ { // 最多等待 5 分钟
		deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, "argo-workflows-workflow-controller", metav1.GetOptions{})
		if err != nil {
			if !errors.IsNotFound(err) {
				return fmt.Errorf("获取 workflow-controller deployment 失败: %w", err)
			}
		} else {
			if deployment.Status.ReadyReplicas > 0 {
				logger.Info("workflow-controller 已就绪", "namespace", namespace)
				break
			}
		}

		if i == 29 {
			return fmt.Errorf("等待 workflow-controller 就绪超时")
		}

		logger.Info("等待 workflow-controller 就绪中...", "attempt", i+1, "namespace", namespace)
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(10 * time.Second):
		}
	}

	logger.Info("Argo Workflows 部署已就绪", "namespace", namespace)
	return nil
}

// generateArgoWorkflowsValues 为指定集群生成 Argo Workflows Helm values 配置
func (m *ArgoWorkflowManager) generateArgoWorkflowsValues(kceClusterID string) string {
	// 基于集群ID生成唯一的主机名，使用集群ID的前8位字符
	shortID := kceClusterID
	if len(shortID) > 8 {
		shortID = shortID[:8]
	}
	hostname := fmt.Sprintf("argo-workflows-%s.kmr-on-kce-pre.ksyun.com", shortID)

	values := fmt.Sprintf(`
server:
  enabled: true
  ingress:
    enabled: true
    ingressClassName: "kmr-on-kce"
    hosts:
      - %s
    paths:
      - /
    pathType: Prefix
    annotations:
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
      kubernetes.io/ingress.class: "kmr-on-kce"
  # 启用服务器认证模式
  authModes:
    - server
  # 额外参数以支持不安全模式访问
  extraArgs:
    - --auth-mode=server
    - --secure=false

# 禁用工作流存档以简化配置
controller:
  persistence: {}
  
# 工作流服务账号配置
workflow:
  serviceAccount:
    create: true
    name: argo-workflow
  rbac:
    create: true
`, hostname)

	return values
}

// GetArgoWorkflowsWebUIURL 获取指定集群的 Argo Workflows Web UI 访问地址
func (m *ArgoWorkflowManager) GetArgoWorkflowsWebUIURL(kceClusterID string) string {
	// 基于集群ID生成唯一的主机名，使用集群ID的前8位字符
	shortID := kceClusterID
	if len(shortID) > 8 {
		shortID = shortID[:8]
	}
	hostname := fmt.Sprintf("argo-workflows-%s.kmr-on-kce-pre.ksyun.com", shortID)
	return fmt.Sprintf("https://%s", hostname)
}
