package workflow

// CreateSparkOperatorTemplate 创建 Spark Operator 安装模板
func CreateSparkOperatorTemplate() WorkflowTemplate {
	return WorkflowTemplate{
		Name: "install-spark-operator",
		Script: &WorkflowScriptSpec{
			Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
			Command: []string{"/bin/sh"},
			Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Spark Operator..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 清理可能存在的旧资源
echo "清理可能存在的旧资源..."
helm uninstall spark-operator -n kmrspark || true
kubectl delete namespace kmrspark || true

# 全新安装Spark Operator
echo "全新安装Spark Operator..."
helm install spark-operator kmr-on-kce/spark-operator \
  --namespace kmrspark \
  --create-namespace \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/spark-operator \
  --set image.tag=2.0.0-36 \
  --set sparkJobNamespaces[0]=kmrspark \
  --set uiService.enable=true \
  --set rbac.createClusterRole=true \
  --set rbac.createRole=true \
  --set serviceAccounts.sparkoperator.create=true \
  --set serviceAccounts.spark.create=true \
  --set controllerThreads=40 \
  --set resyncInterval=30 \
  --set webhook.enable=true \
  --set metrics.enable=true \
  --set metrics.port=10254 \
  --set resources.limits.cpu="4" \
  --set resources.limits.memory="8Gi" \
  --set resources.requests.cpu="100m" \
  --set resources.requests.memory="1Gi" \
  --wait --timeout=10m

echo "✓ Spark Operator 安装完成"

# 验证安装
echo "验证 Spark Operator 安装..."
kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
kubectl get pods -n kmrspark
echo "✓ Spark Operator 验证成功"
`,
		},
		Inputs: &WorkflowInputs{
			Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
				{Name: "kce-cluster-id"},
			},
		},
	}
}

// CreateSparkHistoryTemplate 创建 Spark History Server 安装模板
func CreateSparkHistoryTemplate() WorkflowTemplate {
	return WorkflowTemplate{
		Name: "install-spark-history",
		Script: &WorkflowScriptSpec{
			Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
			Command: []string{"/bin/sh"},
			Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始安装 Spark History Server..."

# 添加并更新helm仓库
echo "添加Helm仓库..."
helm repo add kmr-on-kce https://hub.kce.ksyun.com/chartrepo/kmr-on-kce
helm repo update

# 检查是否已经安装了Spark History
if helm list -n kmrspark | grep -q "spark-history"; then
    echo "Spark History Server已存在，跳过安装"
    exit 0
fi

# 配置参数
CLUSTER_ID="{{inputs.parameters.cluster-id}}"
AK="test-access-key"
SK="test-secret-key" 
SPARK_LOG_DIR="ks3://spark-logs/history/${CLUSTER_ID}"
INGRESS_HOST="spark-history-${CLUSTER_ID}.kmr-on-kce.ksyun.com"

echo "配置信息："
echo "  Cluster ID: $CLUSTER_ID"
echo "  Log Directory: $SPARK_LOG_DIR"
echo "  Ingress Host: $INGRESS_HOST"

# 安装Spark History Server
echo "安装Spark History Server..."
helm install spark-history kmr-on-kce/spark-history \
  --namespace kmrspark \
  --set image.repository=hub.kce.ksyun.com/bigdata-platform/pyspark \
  --set image.tag=v3.4.3-ksc1.3 \
  --set ks3.endpoint=ks3-cn-beijing-internal.ksyuncs.com \
  --set ks3.AccessKey="$AK" \
  --set ks3.AccessSecret="$SK" \
  --set ks3.logDirectory="$SPARK_LOG_DIR" \
  --set ingress.enabled=true \
  --set ingress.host="$INGRESS_HOST" \
  --set ingress.className="kmr-on-kce" \
  --set service.type=ClusterIP \
  --set service.port=18080 \
  --set resources.limits.cpu="500m" \
  --set resources.limits.memory="1Gi" \
  --set resources.requests.cpu="200m" \
  --set resources.requests.memory="512Mi" \
  --wait --timeout=10m

echo "✓ Spark History Server 安装完成"

# 验证安装
echo "验证Spark History Server安装..."
kubectl wait --for=condition=available --timeout=300s deployment/spark-history -n kmrspark
kubectl get deployment spark-history -n kmrspark
kubectl get service spark-history -n kmrspark
kubectl get ingress -n kmrspark
echo "✓ Spark History Server 验证成功"
`,
		},
		Inputs: &WorkflowInputs{
			Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
				{Name: "kce-cluster-id"},
			},
		},
	}
}

// CreateSparkUninstallTemplate 创建 Spark 服务卸载模板
func CreateSparkUninstallTemplate() WorkflowTemplate {
	return WorkflowTemplate{
		Name: "uninstall-spark-services",
		Script: &WorkflowScriptSpec{
			Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
			Command: []string{"/bin/sh"},
			Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始卸载 Spark 服务..."

# 卸载 Spark History Server
echo "卸载 Spark History Server..."
if helm list -n kmrspark | grep -q "spark-history"; then
    echo "找到 Spark History Server，开始卸载..."
    helm uninstall spark-history -n kmrspark
    echo "✓ Spark History Server 卸载完成"
else
    echo "Spark History Server 未安装，跳过卸载"
fi

# 卸载 Spark Operator
echo "卸载 Spark Operator..."
if kubectl get namespace kmrspark >/dev/null 2>&1; then
    echo "找到 Spark Operator，开始卸载..."
    
    # 卸载 Helm release
    helm uninstall spark-operator -n kmrspark || true
    
    # 强制删除残留资源
    kubectl delete deployment spark-operator -n kmrspark --ignore-not-found=true
    kubectl delete service spark-operator -n kmrspark --ignore-not-found=true
    kubectl delete serviceaccount spark-operator -n kmrspark --ignore-not-found=true
    kubectl delete clusterrole spark-operator --ignore-not-found=true
    kubectl delete clusterrolebinding spark-operator --ignore-not-found=true
    
    # 删除命名空间
    kubectl delete namespace kmrspark --ignore-not-found=true
    
    echo "✓ Spark Operator 卸载完成"
else
    echo "Spark Operator 未安装，跳过卸载"
fi

echo "✓ Spark 服务卸载完成"
`,
		},
		Inputs: &WorkflowInputs{
			Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
			},
		},
	}
}

// CreateSparkVerifyTemplate 创建 Spark 服务验证模板
func CreateSparkVerifyTemplate() WorkflowTemplate {
	return WorkflowTemplate{
		Name: "verify-spark-services",
		Script: &WorkflowScriptSpec{
			Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
			Command: []string{"/bin/sh"},
			Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "验证 Spark 服务状态..."

# 检查 Spark Operator
echo "验证 Spark Operator 部署状态..."
if kubectl get deployment spark-operator -n kmrspark >/dev/null 2>&1; then
    kubectl wait --for=condition=available --timeout=300s deployment/spark-operator -n kmrspark
    echo "✓ Spark Operator 验证成功"
else
    echo "⚠ Spark Operator 未安装"
fi

# 检查Spark History（如果安装了）
if kubectl get deployment spark-history -n kmrspark >/dev/null 2>&1; then
    echo "验证 Spark History 部署状态..."
    kubectl wait --for=condition=available --timeout=300s deployment/spark-history -n kmrspark
    echo "✓ Spark History 验证成功"
else
    echo "⚠ Spark History 未安装"
fi

# 显示服务状态
echo "Spark 服务状态："
kubectl get pods -n kmrspark || echo "kmrspark 命名空间不存在"
kubectl get services -n kmrspark || echo "kmrspark 命名空间不存在"
kubectl get ingress -n kmrspark || echo "kmrspark 命名空间不存在"

echo "✓ Spark 服务验证完成"
`,
		},
		Inputs: &WorkflowInputs{
			Parameters: []WorkflowParameter{
				{Name: "cluster-id"},
			},
		},
	}
}

// kubectlInClusterSetupShell kubectl 集群内配置脚本
const kubectlInClusterSetupShell = `
# 配置 kubectl 使用 in-cluster ServiceAccount 凭证
if [ -n "$KUBERNETES_SERVICE_HOST" ] && [ -f "/var/run/secrets/kubernetes.io/serviceaccount/token" ]; then
  echo "[setup] 配置 kubectl 使用 in-cluster ServiceAccount 凭证"
  export K8S_HOST="https://${KUBERNETES_SERVICE_HOST}:${KUBERNETES_SERVICE_PORT:-443}"
  cat >/tmp/kubeconfig <<EOF
apiVersion: v1
kind: Config
clusters:
- cluster:
    certificate-authority: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    server: ${K8S_HOST}
  name: in-cluster
contexts:
- context:
    cluster: in-cluster
    user: in-cluster-user
  name: in-cluster
current-context: in-cluster
users:
- name: in-cluster-user
  user:
    tokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
EOF
  export KUBECONFIG=/tmp/kubeconfig
  echo "[setup] kubectl 配置完成"
else
  echo "[setup] 未检测到 in-cluster 环境，使用默认配置"
fi
`
