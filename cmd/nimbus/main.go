package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/kingsoft/nimbus/internal/api"
	"github.com/kingsoft/nimbus/pkg/config"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// @title Nimbus API
// @version 1.0
// @description Nimbus 云平台 PaaS 框架 API 服务
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.kingsoft.com/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

func main() {
	// 初始化配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger.InitLogger(cfg.LogLevel)

	// 初始化数据库连接
	if err := api.InitDB(cfg.Database); err != nil {
		logger.Error("数据库初始化失败", "error", err)
		os.Exit(1)
	}

	// 初始化路由和服务
	router, workflowService := api.SetupRouter()

	// 启动Pod监控
	ctx := context.Background()
	workflowService.StartPodMonitor(ctx)
	defer workflowService.StopPodMonitor()

	// 配置服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// 启动服务器（非阻塞）
	go func() {
		logger.Info("启动服务器", "port", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("服务器启动失败", "error", err)
			os.Exit(1)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("关闭服务器...")

	// 设置 5 秒的超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("服务器强制关闭", "error", err)
		os.Exit(1)
	}

	logger.Info("服务器已优雅关闭")
}
