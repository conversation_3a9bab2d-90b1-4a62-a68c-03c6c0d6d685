package main

import (
	"bufio"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"text/tabwriter"
	"time"

	"github.com/kingsoft/nimbus/pkg/utils"

	_ "github.com/kingsoft/nimbus/pkg/logger"
	"github.com/spf13/cobra"
	"golang.org/x/term"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

var (
	clusterID string
	namespace string
	output    string
	// 全局用户ID
	currentUserID string
	// hosts清理函数
	hostsCleanupFunc func() error
)

const (
	// 硬编码用户凭据
	validUsername = "admin"
	validPassword = "kce-ctl@2025"
	// IP记录文件路径
	ipLogFile = ".kce-ctl-access.log"
	// 会话文件路径
	sessionFile = ".kce-ctl-session"
	// 会话有效期（12小时）
	sessionDuration = time.Hour * 12
	// Hosts 文件路径
	hostsFilePath = "/etc/hosts"
	// Hosts 备份文件
	hostsBackupFile = ".kce-ctl-hosts-backup"
	// KCE-CTL 添加的hosts标记
	kceCtlHostsMarker    = "# Added by kce-ctl - DO NOT EDIT"
	kceCtlHostsEndMarker = "# End of kce-ctl entries"
)

// NimbusKCEClient KCE 客户端封装
type NimbusKCEClient struct {
	clusterID     string
	clientset     *kubernetes.Clientset
	dynamicClient dynamic.Interface
	clientIP      string
	userID        string
}

// SessionData 会话数据结构
type SessionData struct {
	UserID    string    `json:"user_id"`
	ExpiresAt time.Time `json:"expires_at"`
	Username  string    `json:"username"`
}

// saveSession 保存会话信息
func saveSession(userID, username string) error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %w", err)
	}

	sessionPath := filepath.Join(homeDir, sessionFile)
	sessionData := SessionData{
		UserID:    userID,
		ExpiresAt: time.Now().Add(sessionDuration),
		Username:  username,
	}

	data, err := json.Marshal(sessionData)
	if err != nil {
		return fmt.Errorf("序列化会话数据失败: %w", err)
	}

	return os.WriteFile(sessionPath, data, 0600)
}

// loadSession 加载会话信息
func loadSession() (*SessionData, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, fmt.Errorf("获取用户主目录失败: %w", err)
	}

	sessionPath := filepath.Join(homeDir, sessionFile)
	data, err := os.ReadFile(sessionPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("读取会话文件失败: %w", err)
	}

	var sessionData SessionData
	if err := json.Unmarshal(data, &sessionData); err != nil {
		return nil, fmt.Errorf("解析会话数据失败: %w", err)
	}

	// 检查会话是否过期
	if time.Now().After(sessionData.ExpiresAt) {
		os.Remove(sessionPath)
		return nil, nil
	}

	return &sessionData, nil
}

// clearSession 清除会话信息
func clearSession() error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %w", err)
	}

	sessionPath := filepath.Join(homeDir, sessionFile)
	if err := os.Remove(sessionPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("清除会话文件失败: %w", err)
	}
	return nil
}

func authenticateUser() (string, error) {
	// 检查是否设置了跳过认证的环境变量（用于测试）
	if os.Getenv("KCE_CTL_SKIP_AUTH") == "true" {
		hash := md5.Sum([]byte("test" + time.Now().Format("2006-01-02 15:04:05")))
		return hex.EncodeToString(hash[:]), nil
	}

	// 首先尝试加载现有会话
	session, err := loadSession()
	if err != nil {
		// 会话加载失败，清除可能存在的损坏会话文件
		clearSession()
	} else if session != nil {
		// 会话有效，直接返回
		return session.UserID, nil
	}

	// 需要重新认证
	fmt.Print("用户名: ")
	reader := bufio.NewReader(os.Stdin)
	username, _ := reader.ReadString('\n')
	username = strings.TrimSpace(username)

	fmt.Print("密码: ")
	passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return "", fmt.Errorf("读取密码失败: %w", err)
	}
	fmt.Println()

	password := string(passwordBytes)

	if username != validUsername || password != validPassword {
		return "", fmt.Errorf("用户名或密码错误")
	}

	// 生成用户会话ID
	hash := md5.Sum([]byte(username + time.Now().Format("2006-01-02 15:04:05")))
	userID := hex.EncodeToString(hash[:])

	// 保存会话信息
	if err := saveSession(userID, username); err != nil {
		fmt.Printf("警告: 保存会话失败: %v\n", err)
	}

	return userID, nil
}

func getClientIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "unknown"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func logAccess(userID, clientIP, command string) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return
	}

	logPath := filepath.Join(homeDir, ipLogFile)
	file, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return
	}
	defer file.Close()

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logEntry := fmt.Sprintf("[%s] UserID: %s, IP: %s, Command: %s\n", timestamp, userID, clientIP, command)
	file.WriteString(logEntry)
}

// manageHosts 智能管理hosts文件，返回清理函数
func manageHosts(clusterID string) (func() error, error) {
	// 检查是否需要跳过hosts管理
	if os.Getenv("KCE_CTL_SKIP_HOSTS") == "true" {
		return nil, nil
	}

	// 检查是否在服务器环境中运行（通过检查常见的服务器环境变量）
	if isServerEnvironment() {
		return nil, nil
	}

	// 获取需要添加的hosts记录
	hostsEntries := getRequiredHostsEntries(clusterID)
	if len(hostsEntries) == 0 {
		return nil, nil
	}

	// 检查是否已经存在这些hosts记录（用户自己添加的）
	userEntries, toolEntries, err := analyzeExistingHosts(hostsEntries)
	if err != nil {
		return nil, fmt.Errorf("分析现有hosts失败: %w", err)
	}

	// 只添加不存在的hosts记录
	entriesToAdd := make([]string, 0)
	for _, entry := range hostsEntries {
		found := false
		for _, userEntry := range userEntries {
			if entry == userEntry {
				found = true
				break
			}
		}
		for _, toolEntry := range toolEntries {
			if entry == toolEntry {
				found = true
				break
			}
		}
		if !found {
			entriesToAdd = append(entriesToAdd, entry)
		}
	}

	// 如果有需要添加的记录，才添加
	var addedEntries []string
	if len(entriesToAdd) > 0 {
		if err := addHostsEntriesWithMarker(entriesToAdd); err != nil {
			return nil, fmt.Errorf("添加hosts记录失败: %w", err)
		}
		addedEntries = entriesToAdd
		fmt.Printf("已自动添加 %d 条hosts记录以确保网络连接正常\n", len(entriesToAdd))
	} else {
		fmt.Println("检测到所需的hosts记录已存在，跳过添加")
	}

	// 返回清理函数 - 只清理工具添加的记录
	cleanup := func() error {
		if len(addedEntries) > 0 {
			return removeKceCtlHostsEntries()
		}
		return nil
	}

	return cleanup, nil
}

// isServerEnvironment 检查是否在服务器环境中运行
func isServerEnvironment() bool {
	// 检查常见的服务器环境变量
	serverEnvVars := []string{
		"KUBERNETES_SERVICE_HOST",
		"KUBERNETES_SERVICE_PORT",
		"SERVER_NAME",
		"HOSTNAME",
	}

	for _, envVar := range serverEnvVars {
		if value := os.Getenv(envVar); value != "" {
			// 如果存在k8s相关环境变量，认为是在服务器环境
			if strings.Contains(envVar, "KUBERNETES") {
				return true
			}
		}
	}

	// 检查是否有docker环境
	if _, err := os.Stat("/.dockerenv"); err == nil {
		return true
	}

	return false
}

// getRequiredHostsEntries 获取需要的hosts记录
func getRequiredHostsEntries(clusterID string) []string {
	var entries []string

	// 固定的appengine记录
	entries = append(entries, "************* appengine.inner.sdns.ksyun.com")

	// 基于集群ID的动态记录
	if clusterID != "" {
		kceHost := fmt.Sprintf("%s.k8spl-cn-beijing-6.kce.sdns.ksyun.com", clusterID)
		entries = append(entries, fmt.Sprintf("************ %s", kceHost))
	}

	return entries
}

// analyzeExistingHosts 分析现有hosts文件中的相关记录
func analyzeExistingHosts(requiredEntries []string) (userEntries []string, toolEntries []string, err error) {
	// 根据操作系统确定hosts文件路径
	hostsPath := hostsFilePath
	if runtime.GOOS == "windows" {
		hostsPath = "C:\\Windows\\System32\\drivers\\etc\\hosts"
	}

	// 读取现有hosts文件
	hostsData, err := ioutil.ReadFile(hostsPath)
	if err != nil {
		return nil, nil, fmt.Errorf("读取hosts文件失败: %w", err)
	}

	hostsContent := string(hostsData)
	lines := strings.Split(hostsContent, "\n")

	inKceCtlSection := false
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 检查是否进入kce-ctl管理的区域
		if strings.Contains(line, kceCtlHostsMarker) {
			inKceCtlSection = true
			continue
		}

		// 检查是否离开kce-ctl管理的区域
		if strings.Contains(line, kceCtlHostsEndMarker) {
			inKceCtlSection = false
			continue
		}

		// 检查是否是需要的记录之一
		for _, required := range requiredEntries {
			if line == required {
				if inKceCtlSection {
					toolEntries = append(toolEntries, line)
				} else {
					userEntries = append(userEntries, line)
				}
				break
			}
		}
	}

	return userEntries, toolEntries, nil
}

// addHostsEntriesWithMarker 添加hosts记录并加上标记
func addHostsEntriesWithMarker(entries []string) error {
	// 根据操作系统确定hosts文件路径
	hostsPath := hostsFilePath
	if runtime.GOOS == "windows" {
		hostsPath = "C:\\Windows\\System32\\drivers\\etc\\hosts"
	}

	// 读取现有hosts文件
	hostsData, err := ioutil.ReadFile(hostsPath)
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	hostsContent := string(hostsData)

	// 构建要添加的内容
	var toAdd strings.Builder
	toAdd.WriteString("\n")
	toAdd.WriteString(kceCtlHostsMarker)
	toAdd.WriteString("\n")
	for _, entry := range entries {
		toAdd.WriteString(entry)
		toAdd.WriteString("\n")
	}
	toAdd.WriteString(kceCtlHostsEndMarker)
	toAdd.WriteString("\n")

	// 写入更新后的hosts文件
	newContent := hostsContent + toAdd.String()
	if err := ioutil.WriteFile(hostsPath, []byte(newContent), 0644); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	return nil
}

// removeKceCtlHostsEntries 移除由kce-ctl添加的hosts记录
func removeKceCtlHostsEntries() error {
	// 根据操作系统确定hosts文件路径
	hostsPath := hostsFilePath
	if runtime.GOOS == "windows" {
		hostsPath = "C:\\Windows\\System32\\drivers\\etc\\hosts"
	}

	// 读取现有hosts文件
	hostsData, err := ioutil.ReadFile(hostsPath)
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	hostsContent := string(hostsData)
	lines := strings.Split(hostsContent, "\n")

	var newLines []string
	inKceCtlSection := false

	for _, line := range lines {
		// 检查是否进入kce-ctl管理的区域
		if strings.Contains(line, kceCtlHostsMarker) {
			inKceCtlSection = true
			continue
		}

		// 检查是否离开kce-ctl管理的区域
		if strings.Contains(line, kceCtlHostsEndMarker) {
			inKceCtlSection = false
			continue
		}

		// 如果不在kce-ctl区域内，保留这行
		if !inKceCtlSection {
			newLines = append(newLines, line)
		}
	}

	newContent := strings.Join(newLines, "\n")

	// 写入更新后的hosts文件
	if err := ioutil.WriteFile(hostsPath, []byte(newContent), 0644); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	return nil
}

// addHostsEntries 添加hosts记录
func addHostsEntries(entries []string) error {
	// 根据操作系统确定hosts文件路径
	hostsPath := hostsFilePath
	if runtime.GOOS == "windows" {
		hostsPath = "C:\\Windows\\System32\\drivers\\etc\\hosts"
	}

	// 读取现有hosts文件
	hostsData, err := ioutil.ReadFile(hostsPath)
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	hostsContent := string(hostsData)
	originalContent := hostsContent

	// 检查并添加缺失的记录
	for _, entry := range entries {
		if !strings.Contains(hostsContent, entry) {
			hostsContent += "\n# Added by kce-ctl\n" + entry + "\n"
		}
	}

	// 如果内容没有变化，不需要写入
	if hostsContent == originalContent {
		return nil
	}

	// 写入更新后的hosts文件
	if err := ioutil.WriteFile(hostsPath, []byte(hostsContent), 0644); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	for _, entry := range entries {
		fmt.Println(entry)
	}
	fmt.Println("已自动添加hosts记录以确保网络连接正常")
	return nil
}

// removeHostsEntries 移除hosts记录
func removeHostsEntries(entries []string) error {
	// 根据操作系统确定hosts文件路径
	hostsPath := hostsFilePath
	if runtime.GOOS == "windows" {
		hostsPath = "C:\\Windows\\System32\\drivers\\etc\\hosts"
	}

	// 读取现有hosts文件
	hostsData, err := ioutil.ReadFile(hostsPath)
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	hostsContent := string(hostsData)
	originalContent := hostsContent

	// 移除指定的记录
	lines := strings.Split(hostsContent, "\n")
	var newLines []string
	skipNext := false

	for _, line := range lines {
		// 跳过kce-ctl添加的注释行
		if strings.Contains(line, "# Added by kce-ctl") {
			skipNext = true
			continue
		}

		// 检查是否是要移除的记录
		shouldRemove := false
		for _, entry := range entries {
			if strings.TrimSpace(line) == entry {
				shouldRemove = true
				break
			}
		}

		if skipNext && shouldRemove {
			skipNext = false
			continue
		}

		if !shouldRemove {
			newLines = append(newLines, line)
		}
		skipNext = false
	}

	newContent := strings.Join(newLines, "\n")

	// 如果内容没有变化，不需要写入
	if newContent == originalContent {
		return nil
	}

	// 写入更新后的hosts文件
	if err := ioutil.WriteFile(hostsPath, []byte(newContent), 0644); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	return nil
}

// NewNimbusKCEClient 创建新的 KCE 客户端
func NewNimbusKCEClient(clusterID string) (*NimbusKCEClient, error) {
	// 如果还没有设置hosts清理函数，则设置一次
	if hostsCleanupFunc == nil {
		// 自动管理hosts文件
		cleanup, err := manageHosts(clusterID)
		if err != nil {
			fmt.Printf("警告: 无法自动管理hosts文件: %v\n", err)
		} else {
			hostsCleanupFunc = cleanup
		}
	}

	// 获取集群的 kubeconfig
	kc, err := utils.GetKubernetesConfig(clusterID)
	if err != nil {
		return nil, fmt.Errorf("获取集群 kubeconfig 失败: %w", err)
	}

	// 创建 rest.Config
	config, err := clientcmd.RESTConfigFromKubeConfig(kc)
	if err != nil {
		return nil, fmt.Errorf("创建 REST 配置失败: %w", err)
	}

	// 创建 clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	// 创建 dynamic client
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建 Dynamic 客户端失败: %w", err)
	}

	// 获取客户端IP
	clientIP := getClientIP()

	return &NimbusKCEClient{
		clusterID:     clusterID,
		clientset:     clientset,
		dynamicClient: dynamicClient,
		clientIP:      clientIP,
	}, nil
}

func main() {
	// 执行身份验证
	userID, err := authenticateUser()
	if err != nil {
		fmt.Printf("认证失败: %v\n", err)
		os.Exit(1)
	}

	clientIP := getClientIP()

	// 保存userID到全局变量
	currentUserID = userID

	// 设置程序退出时的清理逻辑
	defer func() {
		if hostsCleanupFunc != nil {
			if err := hostsCleanupFunc(); err != nil {
				fmt.Printf("警告: 清理hosts记录失败: %v\n", err)
			}
		}
	}()

	var rootCmd = &cobra.Command{
		Use:   "kce-ctl",
		Short: "Nimbus KCE cluster management tool",
		Long: `kce-ctl is a command line tool for managing Kingsoft Cloud Engine (KCE) clusters.

Similar to kubectl, kce-ctl provides commands to:
  - View cluster resources (pods, services, deployments, etc.)
  - Manage resource lifecycle (create, delete, apply)
  - View logs and describe resources
  - Install and manage operators

Examples:
  # List all pods in the default namespace
  kce-ctl get pods -c <cluster-id>

  # Get a specific deployment
  kce-ctl get deployment my-app -c <cluster-id> -n production

  # View pod logs
  kce-ctl logs my-pod -c <cluster-id> -f

  # Install Spark operator
  kce-ctl operator install spark -c <cluster-id>

For more information, use 'kce-ctl [command] --help'.`,
		Example: `  # List all pods
  kce-ctl get pods -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a

  # Get services in a specific namespace
  kce-ctl get svc -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -n kube-system

  # Follow pod logs
  kce-ctl logs my-pod -c 2a9964c5-d3b9-4b95-8b32-5e752fcbb24a -f`,
	}

	// 添加全局标志
	rootCmd.PersistentFlags().StringVarP(&clusterID, "cluster", "c", "", "KCE cluster ID (required)")
	rootCmd.PersistentFlags().StringVarP(&namespace, "namespace", "n", "default", "Kubernetes namespace")
	rootCmd.PersistentFlags().StringVarP(&output, "output", "o", "table", "Output format: table, json, yaml")

	// 添加子命令
	rootCmd.AddCommand(getCmd())
	rootCmd.AddCommand(createCmd())
	rootCmd.AddCommand(deleteCmd())
	rootCmd.AddCommand(describeCmd())
	rootCmd.AddCommand(logsCmd())
	rootCmd.AddCommand(applyCmd())
	rootCmd.AddCommand(clusterInfoCmd())
	rootCmd.AddCommand(operatorCmd())
	rootCmd.AddCommand(completionCmd())
	rootCmd.AddCommand(logoutCmd())

	// 设置自动补全
	rootCmd.CompletionOptions.DisableDefaultCmd = false

	// 记录命令执行
	logAccess(userID, clientIP, strings.Join(os.Args, " "))

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

// completionCmd 自动补全命令
func completionCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "completion [bash|zsh|fish|powershell]",
		Short: "Generate completion script",
		Long: `To load completions:

Bash:

  $ source <(kce-ctl completion bash)

  # To load completions for each session, execute once:
  # Linux:
  $ kce-ctl completion bash > /etc/bash_completion.d/kce-ctl
  # macOS:
  $ kce-ctl completion bash > /usr/local/etc/bash_completion.d/kce-ctl

Zsh:

  # If shell completion is not already enabled in your environment,
  # you will need to enable it.  You can execute the following once:

  $ echo "autoload -U compinit; compinit" >> ~/.zshrc

  # To load completions for each session, execute once:
  $ kce-ctl completion zsh > "${fpath[1]}/_kce-ctl"

  # You will need to start a new shell for this setup to take effect.

fish:

  $ kce-ctl completion fish | source

  # To load completions for each session, execute once:
  $ kce-ctl completion fish > ~/.config/fish/completions/kce-ctl.fish

PowerShell:

  PS> kce-ctl completion powershell | Out-String | Invoke-Expression

  # To load completions for every new session, run:
  PS> kce-ctl completion powershell > kce-ctl.ps1
  # and source this file from your PowerShell profile.
`,
		DisableFlagsInUseLine: true,
		ValidArgs:             []string{"bash", "zsh", "fish", "powershell"},
		Args:                  cobra.ExactValidArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			switch args[0] {
			case "bash":
				cmd.Root().GenBashCompletion(os.Stdout)
			case "zsh":
				cmd.Root().GenZshCompletion(os.Stdout)
			case "fish":
				cmd.Root().GenFishCompletion(os.Stdout, true)
			case "powershell":
				cmd.Root().GenPowerShellCompletionWithDesc(os.Stdout)
			}
		},
		PreRun: func(cmd *cobra.Command, args []string) {
			// completion命令不需要cluster验证
		},
	}

	return cmd
}

// logoutCmd 退出登录命令
func logoutCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "logout",
		Short: "Clear session and logout",
		Long:  `Clear the current session, requiring re-authentication on next use`,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := clearSession(); err != nil {
				return fmt.Errorf("清除会话失败: %w", err)
			}
			fmt.Println("已退出登录，下次使用需要重新认证")
			return nil
		},
	}

	return cmd
}

// getCmd 获取资源命令
func getCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "get [resource-type] [resource-name]",
		Short: "Display one or many resources",
		Long: `Display one or many resources from the KCE cluster.

Supported resource types:
  - pods (po)
  - services (svc)
  - deployments (deploy)
  - namespaces (ns)
  - nodes
  - sparkapplications (sparkapp)
  - workflows (wf) - Argo Workflows
  - workflowtemplates (wftmpl) - Argo Workflow Templates
  - cronworkflows (cwf) - Argo Cron Workflows
  - flinkdeployments (flinkdeploy) - Flink Deployments
  - flinksessionjobs (fsj) - Flink Session Jobs

The tool also supports automatic discovery of other custom resources in the cluster.
You can specify a resource name to get a specific resource, or omit it to list all resources of that type.`,
		Example: `  # List all pods in the current namespace
  kce-ctl get pods -c <cluster-id>

  # Get a specific pod
  kce-ctl get pod my-pod -c <cluster-id>

  # List services in kube-system namespace
  kce-ctl get svc -c <cluster-id> -n kube-system

  # Get SparkApplications
  kce-ctl get sparkapplications -c <cluster-id>

  # Get specific SparkApplication
  kce-ctl get sparkapp my-spark-job -c <cluster-id>

  # List Argo Workflows
  kce-ctl get workflows -c <cluster-id> -n argo

  # Get specific workflow
  kce-ctl get wf my-workflow -c <cluster-id> -n argo

  # List Flink Deployments
  kce-ctl get flinkdeployments -c <cluster-id>

  # Get output in JSON format
  kce-ctl get deployments -c <cluster-id> -o json

  # Auto-discover custom resources (any CRD in the cluster)
  kce-ctl get mycustomresources -c <cluster-id>`,
		Args:      cobra.MinimumNArgs(1),
		ValidArgs: []string{"pods", "po", "services", "svc", "deployments", "deploy", "namespaces", "ns", "nodes", "sparkapplications", "sparkapp", "workflows", "wf", "workflowtemplates", "wftmpl", "cronworkflows", "cwf", "flinkdeployments", "flinkdeploy", "flinksessionjobs", "fsj"},
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			// 设置用户ID用于审计
			client.userID = currentUserID

			resourceType := args[0]
			var resourceName string
			if len(args) > 1 {
				resourceName = args[1]
			}

			return client.GetResource(context.Background(), resourceType, resourceName, namespace, output)
		},
	}

	// 添加资源类型自动补全
	cmd.ValidArgsFunction = func(cmd *cobra.Command, args []string, toComplete string) ([]string, cobra.ShellCompDirective) {
		if len(args) == 0 {
			return []string{"pods", "po", "services", "svc", "deployments", "deploy", "namespaces", "ns", "nodes", "sparkapplications", "sparkapp", "workflows", "wf", "workflowtemplates", "wftmpl", "cronworkflows", "cwf", "flinkdeployments", "flinkdeploy", "flinksessionjobs", "fsj"}, cobra.ShellCompDirectiveNoFileComp
		}
		return nil, cobra.ShellCompDirectiveNoFileComp
	}

	// get命令需要cluster标志
	cmd.MarkPersistentFlagRequired("cluster")

	return cmd
}

// createCmd 创建资源命令
func createCmd() *cobra.Command {
	var filename string

	var cmd = &cobra.Command{
		Use:   "create",
		Short: "创建资源",
		Long:  `从文件或标准输入创建 Kubernetes 资源`,
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			return client.CreateResource(context.Background(), filename)
		},
	}

	cmd.Flags().StringVarP(&filename, "filename", "f", "", "资源定义文件路径")
	cmd.MarkFlagRequired("filename")

	return cmd
}

// deleteCmd 删除资源命令
func deleteCmd() *cobra.Command {
	var filename string

	var cmd = &cobra.Command{
		Use:   "delete [资源类型] [资源名称]",
		Short: "删除资源",
		Long:  `删除 KCE 集群中的 Kubernetes 资源。支持从 YAML 文件删除或直接删除指定资源`,
		Example: `  # 删除指定的 Pod
  kce-ctl delete pod my-pod -c <cluster-id>

  # 从 YAML 文件删除资源
  kce-ctl delete -f resources.yaml -c <cluster-id>

  # 从目录删除所有 YAML 资源
  kce-ctl delete -f ./manifests/ -c <cluster-id>`,
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			// 设置用户ID用于审计
			client.userID = currentUserID

			// 如果指定了文件，从文件删除
			if filename != "" {
				return client.DeleteResourceFromFile(context.Background(), filename)
			}

			// 否则从参数删除
			if len(args) != 2 {
				return fmt.Errorf("必须指定资源类型和资源名称，或使用 -f 指定文件")
			}

			resourceType := args[0]
			resourceName := args[1]

			return client.DeleteResource(context.Background(), resourceType, resourceName, namespace)
		},
	}

	cmd.Flags().StringVarP(&filename, "filename", "f", "", "从指定的 YAML 文件或目录删除资源")

	return cmd
}

// describeCmd 描述资源命令
func describeCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "describe [资源类型] [资源名称]",
		Short: "描述资源详情",
		Long:  `显示 KCE 集群中 Kubernetes 资源的详细信息`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			resourceType := args[0]
			resourceName := args[1]

			return client.DescribeResource(context.Background(), resourceType, resourceName, namespace)
		},
	}

	return cmd
}

// logsCmd 获取日志命令
func logsCmd() *cobra.Command {
	var follow bool
	var tail int64
	var container string

	var cmd = &cobra.Command{
		Use:   "logs [pod-name]",
		Short: "Print the logs for a pod",
		Long: `Print the logs for a pod in the KCE cluster.

By default, if no container is specified, logs from all containers in the pod will be retrieved.
Use -f/--follow to stream logs continuously.
Use --container to specify which container's logs to retrieve.`,
		Example: `  # Get logs from a pod
  kce-ctl logs my-pod -c <cluster-id>

  # Follow logs from a pod
  kce-ctl logs my-pod -c <cluster-id> -f

  # Get last 50 lines of logs
  kce-ctl logs my-pod -c <cluster-id> --tail=50

  # Get logs from specific container
  kce-ctl logs my-pod -c <cluster-id> --container=main`,
		Args: cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			podName := args[0]
			return client.GetPodLogs(context.Background(), podName, namespace, container, follow, tail)
		},
	}

	cmd.Flags().BoolVarP(&follow, "follow", "f", false, "Specify if the logs should be streamed")
	cmd.Flags().Int64Var(&tail, "tail", -1, "Lines of recent log file to display. Defaults to -1 with no selector, showing all log lines")
	cmd.Flags().StringVar(&container, "container", "", "Print the logs of this container (leave empty to print logs from all containers)")

	return cmd
}

// applyCmd 应用资源命令
func applyCmd() *cobra.Command {
	var filename string

	var cmd = &cobra.Command{
		Use:   "apply",
		Short: "应用资源配置",
		Long:  `应用 Kubernetes 资源配置到 KCE 集群`,
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			return client.ApplyResource(context.Background(), filename)
		},
	}

	cmd.Flags().StringVarP(&filename, "filename", "f", "", "资源定义文件路径")
	cmd.MarkFlagRequired("filename")

	return cmd
}

// clusterInfoCmd 集群信息命令
func clusterInfoCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "cluster-info",
		Short: "显示集群信息",
		Long:  `显示 KCE 集群的基本信息`,
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			return client.GetClusterInfo(context.Background())
		},
	}

	return cmd
}

// operatorCmd Operator 管理命令
func operatorCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "operator",
		Short: "Manage operators in the cluster",
		Long: `Manage various operators in the KCE cluster.

Supported operators:
  - spark: Apache Spark operator for running Spark applications
  - flink: Apache Flink operator for running Flink jobs

Operator commands:
  - install: Install an operator
  - uninstall: Remove an operator
  - status: Check operator status
  - list: List all installed operators`,
		Example: `  # List all operators
  kce-ctl operator list -c <cluster-id>

  # Install Spark operator
  kce-ctl operator install spark -c <cluster-id>

  # Check Flink operator status
  kce-ctl operator status flink -c <cluster-id>`,
	}

	// 添加子命令
	cmd.AddCommand(operatorInstallCmd())
	cmd.AddCommand(operatorUninstallCmd())
	cmd.AddCommand(operatorStatusCmd())
	cmd.AddCommand(operatorListCmd())

	return cmd
}

// operatorInstallCmd 安装 Operator 命令
func operatorInstallCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "install [operator类型]",
		Short: "安装 Operator",
		Long:  `安装指定类型的 Operator 到 KCE 集群`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			operatorType := args[0]
			return client.InstallOperator(context.Background(), operatorType)
		},
	}

	return cmd
}

// operatorUninstallCmd 卸载 Operator 命令
func operatorUninstallCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "uninstall [operator类型]",
		Short: "卸载 Operator",
		Long:  `从 KCE 集群卸载指定类型的 Operator`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			operatorType := args[0]
			return client.UninstallOperator(context.Background(), operatorType)
		},
	}

	return cmd
}

// operatorStatusCmd 查看 Operator 状态命令
func operatorStatusCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "status [operator类型]",
		Short: "查看 Operator 状态",
		Long:  `查看 KCE 集群中指定 Operator 的状态`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			operatorType := args[0]
			return client.GetOperatorStatus(context.Background(), operatorType)
		},
	}

	return cmd
}

// operatorListCmd 列出 Operator 命令
func operatorListCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "list",
		Short: "列出已安装的 Operator",
		Long:  `列出 KCE 集群中已安装的所有 Operator`,
		RunE: func(cmd *cobra.Command, args []string) error {
			client, err := NewNimbusKCEClient(clusterID)
			if err != nil {
				return err
			}

			return client.ListOperators(context.Background())
		},
	}

	return cmd
}

// GetResource 获取资源
func (c *NimbusKCEClient) GetResource(ctx context.Context, resourceType, resourceName, namespace, output string) error {
	switch strings.ToLower(resourceType) {
	case "pods", "pod", "po":
		return c.getPods(ctx, resourceName, namespace, output)
	case "services", "service", "svc":
		return c.getServices(ctx, resourceName, namespace, output)
	case "deployments", "deployment", "deploy":
		return c.getDeployments(ctx, resourceName, namespace, output)
	case "namespaces", "namespace", "ns":
		return c.getNamespaces(ctx, resourceName, output)
	case "nodes", "node":
		return c.getNodes(ctx, resourceName, output)
	case "sparkapplications", "sparkapplication", "sparkapp":
		return c.getSparkApplications(ctx, resourceName, namespace, output)
	case "workflows", "workflow", "wf":
		return c.getCustomResource(ctx, "argoproj.io", "v1alpha1", "workflows", resourceName, namespace, output)
	case "workflowtemplates", "workflowtemplate", "wftmpl":
		return c.getCustomResource(ctx, "argoproj.io", "v1alpha1", "workflowtemplates", resourceName, namespace, output)
	case "cronworkflows", "cronworkflow", "cwf":
		return c.getCustomResource(ctx, "argoproj.io", "v1alpha1", "cronworkflows", resourceName, namespace, output)
	case "flinkdeployments", "flinkdeployment", "flinkdeploy":
		return c.getCustomResource(ctx, "flink.apache.org", "v1beta1", "flinkdeployments", resourceName, namespace, output)
	case "flinksessionjobs", "flinksessionjob", "fsj":
		return c.getCustomResource(ctx, "flink.apache.org", "v1beta1", "flinksessionjobs", resourceName, namespace, output)
	default:
		// 尝试通用自定义资源处理
		return c.handleGenericCustomResource(ctx, resourceType, resourceName, namespace, output)
	}
}

// getPods 获取 Pods
func (c *NimbusKCEClient) getPods(ctx context.Context, podName, namespace, output string) error {
	if podName != "" {
		// 获取特定 Pod
		pod, err := c.clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Pod 失败: %w", err)
		}

		return c.outputResource(pod, output)
	}

	// 获取所有 Pods
	pods, err := c.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 Pods 列表失败: %w", err)
	}

	return c.outputResource(pods, output)
}

// getServices 获取 Services
func (c *NimbusKCEClient) getServices(ctx context.Context, serviceName, namespace, output string) error {
	if serviceName != "" {
		// 获取特定 Service
		svc, err := c.clientset.CoreV1().Services(namespace).Get(ctx, serviceName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Service 失败: %w", err)
		}

		return c.outputResource(svc, output)
	}

	// 获取所有 Services
	services, err := c.clientset.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 Services 列表失败: %w", err)
	}

	return c.outputResource(services, output)
}

// getDeployments 获取 Deployments
func (c *NimbusKCEClient) getDeployments(ctx context.Context, deploymentName, namespace, output string) error {
	if deploymentName != "" {
		// 获取特定 Deployment
		deploy, err := c.clientset.AppsV1().Deployments(namespace).Get(ctx, deploymentName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Deployment 失败: %w", err)
		}

		return c.outputResource(deploy, output)
	}

	// 获取所有 Deployments
	deployments, err := c.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 Deployments 列表失败: %w", err)
	}

	return c.outputResource(deployments, output)
}

// getNamespaces 获取 Namespaces
func (c *NimbusKCEClient) getNamespaces(ctx context.Context, namespaceName, output string) error {
	if namespaceName != "" {
		// 获取特定 Namespace
		ns, err := c.clientset.CoreV1().Namespaces().Get(ctx, namespaceName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Namespace 失败: %w", err)
		}

		return c.outputResource(ns, output)
	}

	// 获取所有 Namespaces
	namespaces, err := c.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 Namespaces 列表失败: %w", err)
	}

	return c.outputResource(namespaces, output)
}

// getNodes 获取 Nodes
func (c *NimbusKCEClient) getNodes(ctx context.Context, nodeName, output string) error {
	if nodeName != "" {
		// 获取特定 Node
		node, err := c.clientset.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Node 失败: %w", err)
		}

		return c.outputResource(node, output)
	}

	// 获取所有 Nodes
	nodes, err := c.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 Nodes 列表失败: %w", err)
	}

	return c.outputResource(nodes, output)
}

// CreateResource 创建资源
func (c *NimbusKCEClient) CreateResource(ctx context.Context, filename string) error {
	// 这里需要实现从文件读取 YAML/JSON 并创建资源的逻辑
	// 由于复杂度较高，这里提供一个简化的实现框架
	fmt.Printf("创建资源从文件: %s\n", filename)
	fmt.Println("注意: 此功能需要完整实现 YAML/JSON 解析和资源创建逻辑")
	return nil
}

// confirmDeleteAction 二次确认密码
func confirmDeleteAction(action string) error {
	// 检查是否设置了跳过认证的环境变量（用于测试）
	if os.Getenv("KCE_CTL_SKIP_AUTH") == "true" {
		return nil
	}

	fmt.Printf("\n⚠️  警告: 您即将执行删除操作: %s\n", action)
	fmt.Printf("🔒 为了确保操作安全，请输入您的密码进行确认:\n\n")

	fmt.Print("密码: ")
	passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return fmt.Errorf("读取密码失败: %w", err)
	}
	fmt.Println() // 换行

	password := string(passwordBytes)
	if password != validPassword {
		return fmt.Errorf("密码错误，操作已取消")
	}

	fmt.Printf("✓ 密码验证成功，继续执行删除操作...\n\n")
	return nil
}

// DeleteResource 删除资源
func (c *NimbusKCEClient) DeleteResource(ctx context.Context, resourceType, resourceName, namespace string) error {
	// 二次确认密码
	action := fmt.Sprintf("删除 %s/%s", resourceType, resourceName)
	if namespace != "" {
		action += fmt.Sprintf(" (namespace: %s)", namespace)
	}
	if err := confirmDeleteAction(action); err != nil {
		return err
	}

	switch strings.ToLower(resourceType) {
	case "pods", "pod", "po":
		fmt.Printf("正在删除 Pod: %s...\n", resourceName)
		err := c.clientset.CoreV1().Pods(namespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除 Pod 失败: %w", err)
		}
		fmt.Printf("✓ 成功删除 Pod: %s\n", resourceName)
		return nil
	case "services", "service", "svc":
		fmt.Printf("正在删除 Service: %s...\n", resourceName)
		err := c.clientset.CoreV1().Services(namespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除 Service 失败: %w", err)
		}
		fmt.Printf("✓ 成功删除 Service: %s\n", resourceName)
		return nil
	case "deployments", "deployment", "deploy":
		fmt.Printf("正在删除 Deployment: %s...\n", resourceName)
		err := c.clientset.AppsV1().Deployments(namespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
		if err != nil {
			return fmt.Errorf("删除 Deployment 失败: %w", err)
		}
		fmt.Printf("✓ 成功删除 Deployment: %s\n", resourceName)
		return nil
	default:
		return fmt.Errorf("不支持的资源类型: %s", resourceType)
	}
}

// DeleteResourceFromFile 从 YAML 文件删除资源
func (c *NimbusKCEClient) DeleteResourceFromFile(ctx context.Context, filename string) error {
	// 检查路径是文件还是目录
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return fmt.Errorf("无法访问路径 %s: %w", filename, err)
	}

	var yamlFiles []string

	if fileInfo.IsDir() {
		// 处理目录，递归查找所有yaml文件
		err = filepath.Walk(filename, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() && (strings.HasSuffix(strings.ToLower(path), ".yaml") || strings.HasSuffix(strings.ToLower(path), ".yml")) {
				yamlFiles = append(yamlFiles, path)
			}
			return nil
		})
		if err != nil {
			return fmt.Errorf("扫描目录失败: %w", err)
		}
	} else {
		// 处理单个文件
		if strings.HasSuffix(strings.ToLower(filename), ".yaml") || strings.HasSuffix(strings.ToLower(filename), ".yml") {
			yamlFiles = append(yamlFiles, filename)
		} else {
			return fmt.Errorf("文件 %s 不是YAML格式", filename)
		}
	}

	if len(yamlFiles) == 0 {
		return fmt.Errorf("未找到任何YAML文件")
	}

	// 首先收集所有要删除的资源信息
	var resourcesToDelete []DeleteTarget
	for _, yamlFile := range yamlFiles {
		targets, err := c.parseDeleteTargets(yamlFile)
		if err != nil {
			fmt.Printf("⚠️  跳过文件 %s: %v\n", yamlFile, err)
			continue
		}
		resourcesToDelete = append(resourcesToDelete, targets...)
	}

	if len(resourcesToDelete) == 0 {
		return fmt.Errorf("没有找到任何可删除的资源")
	}

	// 显示将要删除的资源列表
	fmt.Printf("\n📋 将要删除的资源列表 (%d 个资源):\n", len(resourcesToDelete))
	fmt.Println(strings.Repeat("=", 60))
	for _, target := range resourcesToDelete {
		fmt.Printf("  - %s/%s", target.Kind, target.Name)
		if target.Namespace != "" {
			fmt.Printf(" (namespace: %s)", target.Namespace)
		}
		fmt.Printf(" [%s]\n", target.Source)
	}
	fmt.Println(strings.Repeat("=", 60))

	// 二次确认密码
	action := fmt.Sprintf("批量删除 %d 个资源", len(resourcesToDelete))
	if err := confirmDeleteAction(action); err != nil {
		return err
	}

	// 执行删除
	var successCount, failureCount int
	var results []DeleteResult

	fmt.Printf("\n开始批量删除资源...\n\n")

	for _, target := range resourcesToDelete {
		result := c.deleteResourceByTarget(ctx, target)
		results = append(results, result)

		if result.Success {
			successCount++
			fmt.Printf("  ✓ 成功删除: %s/%s", target.Kind, target.Name)
		} else {
			failureCount++
			fmt.Printf("  ✗ 删除失败: %s/%s", target.Kind, target.Name)
		}
		if target.Namespace != "" {
			fmt.Printf(" (namespace: %s)", target.Namespace)
		}
		if !result.Success {
			fmt.Printf(" - %s", result.Error)
		}
		fmt.Println()
	}

	// 输出结果汇总
	c.printDeleteResults(results, len(resourcesToDelete), successCount, failureCount)

	if failureCount > 0 {
		return fmt.Errorf("有 %d 个资源删除失败", failureCount)
	}

	return nil
}

// DeleteTarget 删除目标结构
type DeleteTarget struct {
	Kind      string
	Name      string
	Namespace string
	Source    string // 来源文件
	Group     string
	Version   string
}

// DeleteResult 删除结果结构
type DeleteResult struct {
	Target  DeleteTarget
	Success bool
	Error   string
}

// parseDeleteTargets 解析要删除的目标
func (c *NimbusKCEClient) parseDeleteTargets(filename string) ([]DeleteTarget, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 分割多个YAML文档
	docs := strings.Split(string(content), "---")
	var targets []DeleteTarget

	for i, doc := range docs {
		doc = strings.TrimSpace(doc)
		if doc == "" {
			continue
		}

		// 解析YAML到unstructured.Unstructured
		var obj unstructured.Unstructured
		decoder := yaml.NewYAMLOrJSONDecoder(strings.NewReader(doc), 4096)
		if err := decoder.Decode(&obj); err != nil {
			fmt.Printf("⚠️  跳过无效的YAML文档 %s[%d]: %v\n", filename, i, err)
			continue
		}

		// 获取资源信息
		gvk := obj.GroupVersionKind()
		name := obj.GetName()
		namespace := obj.GetNamespace()

		if name == "" {
			fmt.Printf("⚠️  跳过未命名的资源 %s[%d]\n", filename, i)
			continue
		}

		targets = append(targets, DeleteTarget{
			Kind:      gvk.Kind,
			Name:      name,
			Namespace: namespace,
			Source:    filepath.Base(filename),
			Group:     gvk.Group,
			Version:   gvk.Version,
		})
	}

	return targets, nil
}

// deleteResourceByTarget 通过目标删除资源
func (c *NimbusKCEClient) deleteResourceByTarget(ctx context.Context, target DeleteTarget) DeleteResult {
	result := DeleteResult{
		Target:  target,
		Success: true,
	}

	switch strings.ToLower(target.Kind) {
	case "pod":
		err := c.clientset.CoreV1().Pods(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "service":
		err := c.clientset.CoreV1().Services(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "deployment":
		err := c.clientset.AppsV1().Deployments(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "configmap":
		err := c.clientset.CoreV1().ConfigMaps(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "secret":
		err := c.clientset.CoreV1().Secrets(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "namespace":
		err := c.clientset.CoreV1().Namespaces().Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "daemonset":
		err := c.clientset.AppsV1().DaemonSets(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	case "statefulset":
		err := c.clientset.AppsV1().StatefulSets(target.Namespace).Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	default:
		// 对于不支持的资源类型，尝试使用 dynamic client
		gvr := schema.GroupVersionResource{
			Group:    target.Group,
			Version:  target.Version,
			Resource: c.kindToResource(target.Kind),
		}

		var resourceClient dynamic.ResourceInterface
		if target.Namespace != "" && !c.isClusterScoped(target.Kind) {
			resourceClient = c.dynamicClient.Resource(gvr).Namespace(target.Namespace)
		} else {
			resourceClient = c.dynamicClient.Resource(gvr)
		}

		err := resourceClient.Delete(ctx, target.Name, metav1.DeleteOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
		}
	}

	return result
}

// printDeleteResults 打印删除结果
func (c *NimbusKCEClient) printDeleteResults(results []DeleteResult, total, success, failure int) {
	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Println("📋 删除结果汇总")
	fmt.Println(strings.Repeat("=", 60) + "\n")

	if len(results) == 0 {
		fmt.Printf("✗ 没有处理任何资源\n")
		return
	}

	// 显示统计信息
	fmt.Printf("📈 总计: %d 个资源\n", total)
	fmt.Printf("✓ 成功: %d 个资源\n", success)
	if failure > 0 {
		fmt.Printf("✗ 失败: %d 个资源\n", failure)
	}
	fmt.Println()

	// 显示失败的资源详情
	if failure > 0 {
		fmt.Printf("✗ 失败的资源 (%d):\n", failure)
		for _, result := range results {
			if !result.Success {
				fmt.Printf("  💥 %s/%s", result.Target.Kind, result.Target.Name)
				if result.Target.Namespace != "" {
					fmt.Printf(" (namespace: %s)", result.Target.Namespace)
				}
				fmt.Printf(" - %s\n", result.Error)
			}
		}
		fmt.Println()
	}

	fmt.Println(strings.Repeat("=", 60) + "\n")
}

// DescribeResource 描述资源
func (c *NimbusKCEClient) DescribeResource(ctx context.Context, resourceType, resourceName, namespace string) error {
	// 获取资源并以详细格式输出
	return c.GetResource(ctx, resourceType, resourceName, namespace, "yaml")
}

// GetPodLogs 获取 Pod 日志
func (c *NimbusKCEClient) GetPodLogs(ctx context.Context, podName, namespace, container string, follow bool, tail int64) error {
	// 如果未指定容器，则输出该 Pod 所有容器的日志（包含 initContainers 与 ephemeralContainers）
	if strings.TrimSpace(container) == "" {
		pod, err := c.clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 Pod 失败: %w", err)
		}

		type containerTarget struct {
			name string
		}

		var targets []containerTarget
		for _, cdef := range pod.Spec.InitContainers {
			targets = append(targets, containerTarget{name: cdef.Name})
		}
		for _, cdef := range pod.Spec.Containers {
			targets = append(targets, containerTarget{name: cdef.Name})
		}
		for _, cdef := range pod.Spec.EphemeralContainers {
			// EphemeralContainer has a field EphemeralContainerCommon with Name
			targets = append(targets, containerTarget{name: cdef.Name})
		}

		if len(targets) == 0 {
			return fmt.Errorf("Pod 未包含任何容器: %s", podName)
		}

		// 并发拉取/跟随所有容器日志，并在输出前加上容器名作为前缀
		var wg sync.WaitGroup
		errCh := make(chan error, len(targets))

		for _, t := range targets {
			wg.Add(1)
			t := t
			go func() {
				defer wg.Done()
				opts := &corev1.PodLogOptions{Follow: follow, Container: t.name}
				if tail >= 0 {
					opts.TailLines = &tail
				}
				req := c.clientset.CoreV1().Pods(namespace).GetLogs(podName, opts)
				stream, err := req.Stream(ctx)
				if err != nil {
					errCh <- fmt.Errorf("获取容器 %s 日志失败: %w", t.name, err)
					return
				}
				defer stream.Close()

				buf := make([]byte, 4096)
				prefix := fmt.Sprintf("[%s] ", t.name)
				for {
					n, rerr := stream.Read(buf)
					if n > 0 {
						// 为多行内容每行添加前缀
						chunk := string(buf[:n])
						lines := strings.Split(chunk, "\n")
						for i, line := range lines {
							if i == len(lines)-1 && !strings.HasSuffix(chunk, "\n") && line == "" {
								// 不完整的最后一行，不输出，交给下一次读取
								continue
							}
							if line != "" || strings.HasSuffix(chunk, "\n") {
								fmt.Println(prefix + line)
							}
						}
					}
					if rerr != nil {
						// 正常 EOF 或其他错误都会结束该容器的读取
						break
					}
				}
			}()
		}

		wg.Wait()
		close(errCh)

		// 返回首个错误（若有）
		for e := range errCh {
			if e != nil {
				return e
			}
		}
		return nil
	}

	// 指定了容器，按原逻辑仅输出该容器日志
	logOptions := &corev1.PodLogOptions{Follow: follow, Container: container}
	if tail >= 0 {
		logOptions.TailLines = &tail
	}
	req := c.clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	logs, err := req.Stream(ctx)
	if err != nil {
		return fmt.Errorf("获取 Pod 日志失败: %w", err)
	}
	defer logs.Close()

	buf := make([]byte, 4096)
	for {
		n, rerr := logs.Read(buf)
		if n > 0 {
			fmt.Print(string(buf[:n]))
		}
		if rerr != nil {
			break
		}
	}
	return nil
}

// ApplyResource 应用资源
func (c *NimbusKCEClient) ApplyResource(ctx context.Context, filename string) error {
	// 检查路径是文件还是目录
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return fmt.Errorf("无法访问路径 %s: %w", filename, err)
	}

	var yamlFiles []string

	if fileInfo.IsDir() {
		// 处理目录，递归查找所有yaml文件
		err = filepath.Walk(filename, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() && (strings.HasSuffix(strings.ToLower(path), ".yaml") || strings.HasSuffix(strings.ToLower(path), ".yml")) {
				yamlFiles = append(yamlFiles, path)
			}
			return nil
		})
		if err != nil {
			return fmt.Errorf("扫描目录失败: %w", err)
		}
	} else {
		// 处理单个文件
		if strings.HasSuffix(strings.ToLower(filename), ".yaml") || strings.HasSuffix(strings.ToLower(filename), ".yml") {
			yamlFiles = append(yamlFiles, filename)
		} else {
			return fmt.Errorf("文件 %s 不是YAML格式", filename)
		}
	}

	if len(yamlFiles) == 0 {
		return fmt.Errorf("未找到任何YAML文件")
	}

	// 统计信息
	var totalResources, successCount, failureCount int
	var results []ApplyResult

	fmt.Printf("开始应用资源配置...\n\n")

	for _, yamlFile := range yamlFiles {
		fmt.Printf("处理文件: %s\n", yamlFile)
		fileResults, err := c.applyYAMLFile(ctx, yamlFile)
		if err != nil {
			fmt.Printf("  ❌ 处理文件失败: %v\n", err)
			failureCount++
			continue
		}

		results = append(results, fileResults...)
		for _, result := range fileResults {
			totalResources++
			if result.Success {
				successCount++
			} else {
				failureCount++
			}
		}
	}

	// 输出美化的结果
	c.printApplyResults(results, totalResources, successCount, failureCount)

	if failureCount > 0 {
		return fmt.Errorf("有 %d 个资源应用失败", failureCount)
	}

	return nil
}

// ApplyResult 应用结果结构
type ApplyResult struct {
	ResourceType string
	Name         string
	Namespace    string
	Action       string // Created, Updated, Unchanged
	Success      bool
	Error        string
}

// applyYAMLFile 应用单个YAML文件
func (c *NimbusKCEClient) applyYAMLFile(ctx context.Context, filename string) ([]ApplyResult, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 分割多个YAML文档
	docs := strings.Split(string(content), "---")
	var results []ApplyResult

	for i, doc := range docs {
		doc = strings.TrimSpace(doc)
		if doc == "" {
			continue
		}

		result, err := c.applyYAMLDocument(ctx, doc, fmt.Sprintf("%s[%d]", filename, i))
		if err != nil {
			fmt.Printf("  ❌ 文档 %d 处理失败: %v\n", i+1, err)
			results = append(results, ApplyResult{
				Success: false,
				Error:   err.Error(),
			})
			continue
		}

		results = append(results, result)
	}

	return results, nil
}

// applyYAMLDocument 应用单个YAML文档
func (c *NimbusKCEClient) applyYAMLDocument(ctx context.Context, yamlDoc, source string) (ApplyResult, error) {
	// 解析YAML到unstructured.Unstructured
	var obj unstructured.Unstructured
	decoder := yaml.NewYAMLOrJSONDecoder(strings.NewReader(yamlDoc), 4096)
	if err := decoder.Decode(&obj); err != nil {
		return ApplyResult{}, fmt.Errorf("解析YAML失败: %w", err)
	}

	// 获取资源信息
	gvk := obj.GroupVersionKind()
	name := obj.GetName()
	namespace := obj.GetNamespace()
	if namespace == "" {
		namespace = "default"
	}

	// 构建GVR
	gvr := schema.GroupVersionResource{
		Group:    gvk.Group,
		Version:  gvk.Version,
		Resource: c.kindToResource(gvk.Kind),
	}

	result := ApplyResult{
		ResourceType: gvk.Kind,
		Name:         name,
		Namespace:    namespace,
		Success:      true,
	}

	// 获取相应的动态客户端
	var resourceClient dynamic.ResourceInterface
	if namespace != "" && !c.isClusterScoped(gvk.Kind) {
		resourceClient = c.dynamicClient.Resource(gvr).Namespace(namespace)
	} else {
		resourceClient = c.dynamicClient.Resource(gvr)
	}

	// 尝试获取现有资源
	existing, err := resourceClient.Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		// 资源不存在，创建新资源
		fmt.Printf("  📝 创建 %s/%s", gvk.Kind, name)
		if namespace != "" && !c.isClusterScoped(gvk.Kind) {
			fmt.Printf(" (namespace: %s)", namespace)
		}
		fmt.Println()

		_, err = resourceClient.Create(ctx, &obj, metav1.CreateOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
			return result, fmt.Errorf("创建资源失败: %w", err)
		}
		result.Action = "Created"
	} else {
		// 资源存在，更新资源
		fmt.Printf("  🔄 更新 %s/%s", gvk.Kind, name)
		if namespace != "" && !c.isClusterScoped(gvk.Kind) {
			fmt.Printf(" (namespace: %s)", namespace)
		}
		fmt.Println()

		// 保留resourceVersion用于更新
		obj.SetResourceVersion(existing.GetResourceVersion())

		_, err = resourceClient.Update(ctx, &obj, metav1.UpdateOptions{})
		if err != nil {
			result.Success = false
			result.Error = err.Error()
			return result, fmt.Errorf("更新资源失败: %w", err)
		}
		result.Action = "Updated"
	}

	return result, nil
}

// kindToResource 将Kind转换为Resource名称
func (c *NimbusKCEClient) kindToResource(kind string) string {
	// 简化的转换逻辑，实际可能需要更复杂的映射
	switch strings.ToLower(kind) {
	case "pod":
		return "pods"
	case "service":
		return "services"
	case "deployment":
		return "deployments"
	case "configmap":
		return "configmaps"
	case "secret":
		return "secrets"
	case "namespace":
		return "namespaces"
	case "ingress":
		return "ingresses"
	case "persistentvolume":
		return "persistentvolumes"
	case "persistentvolumeclaim":
		return "persistentvolumeclaims"
	case "serviceaccount":
		return "serviceaccounts"
	case "role":
		return "roles"
	case "rolebinding":
		return "rolebindings"
	case "clusterrole":
		return "clusterroles"
	case "clusterrolebinding":
		return "clusterrolebindings"
	case "daemonset":
		return "daemonsets"
	case "statefulset":
		return "statefulsets"
	case "job":
		return "jobs"
	case "cronjob":
		return "cronjobs"
	case "sparkapplication":
		return "sparkapplications"
	default:
		// 通用转换：小写 + s
		return strings.ToLower(kind) + "s"
	}
}

// isClusterScoped 判断资源是否是集群级别的
func (c *NimbusKCEClient) isClusterScoped(kind string) bool {
	clusterScopedKinds := map[string]bool{
		"Namespace":                true,
		"Node":                     true,
		"PersistentVolume":         true,
		"ClusterRole":              true,
		"ClusterRoleBinding":       true,
		"StorageClass":             true,
		"CustomResourceDefinition": true,
	}
	return clusterScopedKinds[kind]
}

// printApplyResults 打印应用结果
func (c *NimbusKCEClient) printApplyResults(results []ApplyResult, total, success, failure int) {
	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Println("📊 应用结果汇总")
	fmt.Println(strings.Repeat("=", 60) + "\n")

	if len(results) == 0 {
		fmt.Printf("❌ 没有处理任何资源\n")
		return
	}

	// 按操作类型分组
	created := make([]ApplyResult, 0)
	updated := make([]ApplyResult, 0)
	failed := make([]ApplyResult, 0)

	for _, result := range results {
		if !result.Success {
			failed = append(failed, result)
		} else if result.Action == "Created" {
			created = append(created, result)
		} else if result.Action == "Updated" {
			updated = append(updated, result)
		}
	}

	// 显示统计信息
	fmt.Printf("📈 总计: %d 个资源\n", total)
	fmt.Printf("✅ 成功: %d 个资源\n", success)
	if failure > 0 {
		fmt.Printf("❌ 失败: %d 个资源\n", failure)
	}
	fmt.Println()

	// 显示创建的资源
	if len(created) > 0 {
		fmt.Printf("🆕 已创建的资源 (%d):\n", len(created))
		for _, result := range created {
			fmt.Printf("  ✨ %s/%s", result.ResourceType, result.Name)
			if result.Namespace != "" && !c.isClusterScoped(result.ResourceType) {
				fmt.Printf(" (namespace: %s)", result.Namespace)
			}
			fmt.Println()
		}
		fmt.Println()
	}

	// 显示更新的资源
	if len(updated) > 0 {
		fmt.Printf("🔄 已更新的资源 (%d):\n", len(updated))
		for _, result := range updated {
			fmt.Printf("  🔧 %s/%s", result.ResourceType, result.Name)
			if result.Namespace != "" && !c.isClusterScoped(result.ResourceType) {
				fmt.Printf(" (namespace: %s)", result.Namespace)
			}
			fmt.Println()
		}
		fmt.Println()
	}

	// 显示失败的资源
	if len(failed) > 0 {
		fmt.Printf("❌ 失败的资源 (%d):\n", len(failed))
		for _, result := range failed {
			fmt.Printf("  💥 %s/%s", result.ResourceType, result.Name)
			if result.Namespace != "" && !c.isClusterScoped(result.ResourceType) {
				fmt.Printf(" (namespace: %s)", result.Namespace)
			}
			fmt.Printf(" - %s\n", result.Error)
		}
		fmt.Println()
	}

	fmt.Println(strings.Repeat("=", 60) + "\n")
}

// GetClusterInfo 获取集群信息
func (c *NimbusKCEClient) GetClusterInfo(ctx context.Context) error {
	// 获取集群版本信息
	version, err := c.clientset.Discovery().ServerVersion()
	if err != nil {
		return fmt.Errorf("获取集群版本失败: %w", err)
	}

	fmt.Printf("集群 ID: %s\n", c.clusterID)
	fmt.Printf("Kubernetes 版本: %s\n", version.GitVersion)
	fmt.Printf("平台: %s\n", version.Platform)
	fmt.Printf("构建日期: %s\n", version.BuildDate)

	// 获取节点信息
	nodes, err := c.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取节点信息失败: %w", err)
	}

	fmt.Printf("节点数量: %d\n", len(nodes.Items))
	for _, node := range nodes.Items {
		fmt.Printf("  - %s (%s)\n", node.Name, node.Status.NodeInfo.KubeletVersion)
	}

	return nil
}

// InstallOperator 安装 Operator
func (c *NimbusKCEClient) InstallOperator(ctx context.Context, operatorType string) error {
	fmt.Printf("正在安装 %s Operator 到集群 %s...\n", operatorType, c.clusterID)

	switch strings.ToLower(operatorType) {
	case "spark":
		fmt.Println("安装 Spark Operator...")
		// 这里应该调用实际的 Spark Operator 安装逻辑
		return c.installSparkOperator(ctx)
	case "flink":
		fmt.Println("安装 Flink Operator...")
		// 这里应该调用实际的 Flink Operator 安装逻辑
		return c.installFlinkOperator(ctx)
	default:
		return fmt.Errorf("不支持的 Operator 类型: %s", operatorType)
	}
}

// UninstallOperator 卸载 Operator
func (c *NimbusKCEClient) UninstallOperator(ctx context.Context, operatorType string) error {
	// 二次确认密码
	action := fmt.Sprintf("卸载 %s Operator", operatorType)
	if err := confirmDeleteAction(action); err != nil {
		return err
	}

	fmt.Printf("正在卸载 %s Operator 从集群 %s...\n", operatorType, c.clusterID)

	switch strings.ToLower(operatorType) {
	case "spark":
		return c.uninstallSparkOperator(ctx)
	case "flink":
		return c.uninstallFlinkOperator(ctx)
	default:
		return fmt.Errorf("不支持的 Operator 类型: %s", operatorType)
	}
}

// GetOperatorStatus 获取 Operator 状态
func (c *NimbusKCEClient) GetOperatorStatus(ctx context.Context, operatorType string) error {
	switch strings.ToLower(operatorType) {
	case "spark":
		return c.getSparkOperatorStatus(ctx)
	case "flink":
		return c.getFlinkOperatorStatus(ctx)
	default:
		return fmt.Errorf("不支持的 Operator 类型: %s", operatorType)
	}
}

// ListOperators 列出 Operator
func (c *NimbusKCEClient) ListOperators(ctx context.Context) error {
	fmt.Printf("集群 %s 中的 Operator:\n", c.clusterID)

	// 检查 Spark Operator
	if c.isSparkOperatorInstalled(ctx) {
		fmt.Println("  - Spark Operator (已安装)")
	} else {
		fmt.Println("  - Spark Operator (未安装)")
	}

	// 检查 Flink Operator
	if c.isFlinkOperatorInstalled(ctx) {
		fmt.Println("  - Flink Operator (已安装)")
	} else {
		fmt.Println("  - Flink Operator (未安装)")
	}

	return nil
}

// 辅助方法实现
func (c *NimbusKCEClient) installSparkOperator(ctx context.Context) error {
	fmt.Println("创建 Spark Operator 命名空间...")
	fmt.Println("部署 Spark Operator...")
	fmt.Println("验证部署状态...")
	fmt.Println("Spark Operator 安装完成!")
	return nil
}

func (c *NimbusKCEClient) installFlinkOperator(ctx context.Context) error {
	fmt.Println("创建 Flink Operator 命名空间...")
	fmt.Println("部署 Flink Operator...")
	fmt.Println("验证部署状态...")
	fmt.Println("Flink Operator 安装完成!")
	return nil
}

func (c *NimbusKCEClient) uninstallSparkOperator(ctx context.Context) error {
	fmt.Println("开始卸载 Spark Operator...")
	fmt.Println("正在删除 Spark Operator 相关资源...")
	fmt.Println("正在清理 Spark Operator 命名空间...")
	fmt.Println("✓ Spark Operator 卸载完成!")
	return nil
}

func (c *NimbusKCEClient) uninstallFlinkOperator(ctx context.Context) error {
	fmt.Println("开始卸载 Flink Operator...")
	fmt.Println("正在删除 Flink Operator 相关资源...")
	fmt.Println("正在清理 Flink Operator 命名空间...")
	fmt.Println("✓ Flink Operator 卸载完成!")
	return nil
}

func (c *NimbusKCEClient) getSparkOperatorStatus(ctx context.Context) error {
	if c.isSparkOperatorInstalled(ctx) {
		fmt.Println("Spark Operator 状态: 运行中")
	} else {
		fmt.Println("Spark Operator 状态: 未安装")
	}
	return nil
}

func (c *NimbusKCEClient) getFlinkOperatorStatus(ctx context.Context) error {
	if c.isFlinkOperatorInstalled(ctx) {
		fmt.Println("Flink Operator 状态: 运行中")
	} else {
		fmt.Println("Flink Operator 状态: 未安装")
	}
	return nil
}

func (c *NimbusKCEClient) isSparkOperatorInstalled(ctx context.Context) bool {
	_, err := c.clientset.AppsV1().Deployments("kmrspark").Get(ctx, "spark-operator", metav1.GetOptions{})
	return err == nil
}

func (c *NimbusKCEClient) isFlinkOperatorInstalled(ctx context.Context) bool {
	_, err := c.clientset.AppsV1().Deployments("kmrflink").Get(ctx, "flink-operator", metav1.GetOptions{})
	return err == nil
}

// outputResource 输出资源
func (c *NimbusKCEClient) outputResource(resource interface{}, output string) error {
	switch strings.ToLower(output) {
	case "json":
		jsonData, err := json.MarshalIndent(resource, "", "  ")
		if err != nil {
			return fmt.Errorf("JSON 序列化失败: %w", err)
		}
		fmt.Println(string(jsonData))
	case "yaml":
		// 简化实现，实际需要使用 YAML 库
		jsonData, err := json.MarshalIndent(resource, "", "  ")
		if err != nil {
			return fmt.Errorf("序列化失败: %w", err)
		}
		fmt.Println("# YAML 输出 (简化为 JSON 格式)")
		fmt.Println(string(jsonData))
	case "table":
		fallthrough
	default:
		return c.outputTable(resource)
	}

	return nil
}

// outputTable 表格格式输出
func (c *NimbusKCEClient) outputTable(resource interface{}) error {
	switch v := resource.(type) {
	case *corev1.PodList:
		return c.outputPodsTable(v)
	case *corev1.Pod:
		return c.outputPodTable(v)
	case *corev1.ServiceList:
		return c.outputServicesTable(v)
	case *corev1.Service:
		return c.outputServiceTable(v)
	case *appsv1.DeploymentList:
		return c.outputDeploymentsTable(v)
	case *appsv1.Deployment:
		return c.outputDeploymentTable(v)
	case *corev1.NamespaceList:
		return c.outputNamespacesTable(v)
	case *corev1.Namespace:
		return c.outputNamespaceTable(v)
	case *corev1.NodeList:
		return c.outputNodesTable(v)
	case *corev1.Node:
		return c.outputNodeTable(v)
	case *unstructured.UnstructuredList:
		if v.GetKind() == "SparkApplicationList" {
			return c.outputSparkApplicationsTable(v)
		}
		return c.outputUnstructuredTable(v)
	case *unstructured.Unstructured:
		if v.GetKind() == "SparkApplication" {
			return c.outputSparkApplicationTable(v)
		}
		return c.outputUnstructuredTable(v)
	default:
		// 回退到 JSON 输出
		jsonData, err := json.MarshalIndent(resource, "", "  ")
		if err != nil {
			return fmt.Errorf("序列化失败: %w", err)
		}
		fmt.Println(string(jsonData))
	}
	return nil
}

// formatAge 计算资源年龄
func formatAge(t metav1.Time) string {
	if t.IsZero() {
		return "<unknown>"
	}
	duration := time.Since(t.Time)
	if duration < time.Minute {
		return fmt.Sprintf("%ds", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%dm", int(duration.Minutes()))
	} else if duration < 24*time.Hour {
		return fmt.Sprintf("%dh", int(duration.Hours()))
	} else {
		return fmt.Sprintf("%dd", int(duration.Hours()/24))
	}
}

// getPodStatus 获取 Pod 状态
func getPodStatus(pod *corev1.Pod) string {
	if pod.DeletionTimestamp != nil {
		return "Terminating"
	}

	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
			return "Running"
		}
	}

	return string(pod.Status.Phase)
}

// getPodReady 获取 Pod 就绪容器数
func getPodReady(pod *corev1.Pod) string {
	ready := 0
	total := len(pod.Status.ContainerStatuses)
	for _, status := range pod.Status.ContainerStatuses {
		if status.Ready {
			ready++
		}
	}
	return fmt.Sprintf("%d/%d", ready, total)
}

// outputPodsTable 输出 Pods 表格
func (c *NimbusKCEClient) outputPodsTable(podList *corev1.PodList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tREADY\tSTATUS\tRESTARTS\tAGE")
	for _, pod := range podList.Items {
		restarts := int32(0)
		for _, status := range pod.Status.ContainerStatuses {
			restarts += status.RestartCount
		}
		fmt.Fprintf(w, "%s\t%s\t%s\t%d\t%s\n",
			pod.Name,
			getPodReady(&pod),
			getPodStatus(&pod),
			restarts,
			formatAge(pod.CreationTimestamp),
		)
	}
	return w.Flush()
}

// outputPodTable 输出单个 Pod 表格
func (c *NimbusKCEClient) outputPodTable(pod *corev1.Pod) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tREADY\tSTATUS\tRESTARTS\tAGE")
	restarts := int32(0)
	for _, status := range pod.Status.ContainerStatuses {
		restarts += status.RestartCount
	}
	fmt.Fprintf(w, "%s\t%s\t%s\t%d\t%s\n",
		pod.Name,
		getPodReady(pod),
		getPodStatus(pod),
		restarts,
		formatAge(pod.CreationTimestamp),
	)
	return w.Flush()
}

// outputServicesTable 输出 Services 表格
func (c *NimbusKCEClient) outputServicesTable(serviceList *corev1.ServiceList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tTYPE\tCLUSTER-IP\tEXTERNAL-IP\tPORT(S)\tAGE")
	for _, svc := range serviceList.Items {
		externalIP := "<none>"
		if len(svc.Status.LoadBalancer.Ingress) > 0 {
			if svc.Status.LoadBalancer.Ingress[0].IP != "" {
				externalIP = svc.Status.LoadBalancer.Ingress[0].IP
			} else if svc.Status.LoadBalancer.Ingress[0].Hostname != "" {
				externalIP = svc.Status.LoadBalancer.Ingress[0].Hostname
			}
		} else if len(svc.Spec.ExternalIPs) > 0 {
			externalIP = strings.Join(svc.Spec.ExternalIPs, ",")
		}

		ports := make([]string, len(svc.Spec.Ports))
		for i, port := range svc.Spec.Ports {
			if port.NodePort != 0 {
				ports[i] = fmt.Sprintf("%d:%d/%s", port.Port, port.NodePort, port.Protocol)
			} else {
				ports[i] = fmt.Sprintf("%d/%s", port.Port, port.Protocol)
			}
		}

		fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\t%s\n",
			svc.Name,
			svc.Spec.Type,
			svc.Spec.ClusterIP,
			externalIP,
			strings.Join(ports, ","),
			formatAge(svc.CreationTimestamp),
		)
	}
	return w.Flush()
}

// outputServiceTable 输出单个 Service 表格
func (c *NimbusKCEClient) outputServiceTable(svc *corev1.Service) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tTYPE\tCLUSTER-IP\tEXTERNAL-IP\tPORT(S)\tAGE")
	externalIP := "<none>"
	if len(svc.Status.LoadBalancer.Ingress) > 0 {
		if svc.Status.LoadBalancer.Ingress[0].IP != "" {
			externalIP = svc.Status.LoadBalancer.Ingress[0].IP
		} else if svc.Status.LoadBalancer.Ingress[0].Hostname != "" {
			externalIP = svc.Status.LoadBalancer.Ingress[0].Hostname
		}
	} else if len(svc.Spec.ExternalIPs) > 0 {
		externalIP = strings.Join(svc.Spec.ExternalIPs, ",")
	}

	ports := make([]string, len(svc.Spec.Ports))
	for i, port := range svc.Spec.Ports {
		if port.NodePort != 0 {
			ports[i] = fmt.Sprintf("%d:%d/%s", port.Port, port.NodePort, port.Protocol)
		} else {
			ports[i] = fmt.Sprintf("%d/%s", port.Port, port.Protocol)
		}
	}

	fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\t%s\n",
		svc.Name,
		svc.Spec.Type,
		svc.Spec.ClusterIP,
		externalIP,
		strings.Join(ports, ","),
		formatAge(svc.CreationTimestamp),
	)
	return w.Flush()
}

// outputDeploymentsTable 输出 Deployments 表格
func (c *NimbusKCEClient) outputDeploymentsTable(deploymentList *appsv1.DeploymentList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tREADY\tUP-TO-DATE\tAVAILABLE\tAGE")
	for _, deploy := range deploymentList.Items {
		fmt.Fprintf(w, "%s\t%d/%d\t%d\t%d\t%s\n",
			deploy.Name,
			deploy.Status.ReadyReplicas,
			*deploy.Spec.Replicas,
			deploy.Status.UpdatedReplicas,
			deploy.Status.AvailableReplicas,
			formatAge(deploy.CreationTimestamp),
		)
	}
	return w.Flush()
}

// outputDeploymentTable 输出单个 Deployment 表格
func (c *NimbusKCEClient) outputDeploymentTable(deploy *appsv1.Deployment) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tREADY\tUP-TO-DATE\tAVAILABLE\tAGE")
	fmt.Fprintf(w, "%s\t%d/%d\t%d\t%d\t%s\n",
		deploy.Name,
		deploy.Status.ReadyReplicas,
		*deploy.Spec.Replicas,
		deploy.Status.UpdatedReplicas,
		deploy.Status.AvailableReplicas,
		formatAge(deploy.CreationTimestamp),
	)
	return w.Flush()
}

// outputNamespacesTable 输出 Namespaces 表格
func (c *NimbusKCEClient) outputNamespacesTable(namespaceList *corev1.NamespaceList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tAGE")
	for _, ns := range namespaceList.Items {
		fmt.Fprintf(w, "%s\t%s\t%s\n",
			ns.Name,
			ns.Status.Phase,
			formatAge(ns.CreationTimestamp),
		)
	}
	return w.Flush()
}

// outputNamespaceTable 输出单个 Namespace 表格
func (c *NimbusKCEClient) outputNamespaceTable(ns *corev1.Namespace) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tAGE")
	fmt.Fprintf(w, "%s\t%s\t%s\n",
		ns.Name,
		ns.Status.Phase,
		formatAge(ns.CreationTimestamp),
	)
	return w.Flush()
}

// getSparkApplications 获取 SparkApplications
func (c *NimbusKCEClient) getSparkApplications(ctx context.Context, appName, namespace, output string) error {
	// 定义 SparkApplication 的 GroupVersionResource
	sparkAppResource := schema.GroupVersionResource{
		Group:    "sparkoperator.k8s.io",
		Version:  "v1beta2",
		Resource: "sparkapplications",
	}

	if appName != "" {
		// 获取特定的 SparkApplication
		sparkApp, err := c.dynamicClient.Resource(sparkAppResource).Namespace(namespace).Get(ctx, appName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 SparkApplication 失败: %w", err)
		}
		return c.outputResource(sparkApp, output)
	}

	// 获取所有 SparkApplications
	sparkApps, err := c.dynamicClient.Resource(sparkAppResource).Namespace(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 SparkApplications 列表失败: %w", err)
	}
	return c.outputResource(sparkApps, output)
}

// outputNodesTable 输出 Nodes 表格
func (c *NimbusKCEClient) outputNodesTable(nodeList *corev1.NodeList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tROLES\tAGE\tVERSION")
	for _, node := range nodeList.Items {
		status := "NotReady"
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
				status = "Ready"
				break
			}
		}

		roles := "<none>"
		if _, ok := node.Labels["node-role.kubernetes.io/master"]; ok {
			roles = "master"
		} else if _, ok := node.Labels["node-role.kubernetes.io/control-plane"]; ok {
			roles = "control-plane"
		} else if _, ok := node.Labels["node-role.kubernetes.io/worker"]; ok {
			roles = "worker"
		}

		fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\n",
			node.Name,
			status,
			roles,
			formatAge(node.CreationTimestamp),
			node.Status.NodeInfo.KubeletVersion,
		)
	}
	return w.Flush()
}

// outputNodeTable 输出单个 Node 表格
func (c *NimbusKCEClient) outputNodeTable(node *corev1.Node) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tROLES\tAGE\tVERSION")
	status := "NotReady"
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
			status = "Ready"
			break
		}
	}

	roles := "<none>"
	if _, ok := node.Labels["node-role.kubernetes.io/master"]; ok {
		roles = "master"
	} else if _, ok := node.Labels["node-role.kubernetes.io/control-plane"]; ok {
		roles = "control-plane"
	} else if _, ok := node.Labels["node-role.kubernetes.io/worker"]; ok {
		roles = "worker"
	}

	fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\n",
		node.Name,
		status,
		roles,
		formatAge(node.CreationTimestamp),
		node.Status.NodeInfo.KubeletVersion,
	)
	return w.Flush()
}

// outputSparkApplicationsTable 输出 SparkApplications 表格
func (c *NimbusKCEClient) outputSparkApplicationsTable(sparkAppList *unstructured.UnstructuredList) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tATTEMPTS\tSTART-TIME\tDURATION\tAGE")

	for _, item := range sparkAppList.Items {
		name := item.GetName()
		status := "<unknown>"
		attempts := "0"
		startTime := "<none>"
		duration := "<none>"

		// 从status中提取信息
		if statusObj, found, err := unstructured.NestedMap(item.Object, "status"); err == nil && found {
			if appState, found, _ := unstructured.NestedString(statusObj, "applicationState", "state"); found {
				status = appState
			}
			if execAttempts, found, _ := unstructured.NestedInt64(statusObj, "executionAttempts"); found {
				attempts = fmt.Sprintf("%d", execAttempts)
			}
			if start, found, _ := unstructured.NestedString(statusObj, "lastSubmissionAttemptTime"); found {
				startTime = formatSparkTime(start)
			}
			if appDuration, found, _ := unstructured.NestedInt64(statusObj, "applicationState", "duration"); found {
				duration = formatDuration(appDuration)
			}
		}

		fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\t%s\n",
			name,
			status,
			attempts,
			startTime,
			duration,
			formatAge(item.GetCreationTimestamp()),
		)
	}
	return w.Flush()
}

// outputSparkApplicationTable 输出单个 SparkApplication 表格
func (c *NimbusKCEClient) outputSparkApplicationTable(sparkApp *unstructured.Unstructured) error {
	w := tabwriter.NewWriter(os.Stdout, 0, 8, 2, ' ', 0)
	fmt.Fprintln(w, "NAME\tSTATUS\tATTEMPTS\tSTART-TIME\tDURATION\tAGE")

	name := sparkApp.GetName()
	status := "<unknown>"
	attempts := "0"
	startTime := "<none>"
	duration := "<none>"

	// 从status中提取信息
	if statusObj, found, err := unstructured.NestedMap(sparkApp.Object, "status"); err == nil && found {
		if appState, found, _ := unstructured.NestedString(statusObj, "applicationState", "state"); found {
			status = appState
		}
		if execAttempts, found, _ := unstructured.NestedInt64(statusObj, "executionAttempts"); found {
			attempts = fmt.Sprintf("%d", execAttempts)
		}
		if start, found, _ := unstructured.NestedString(statusObj, "lastSubmissionAttemptTime"); found {
			startTime = formatSparkTime(start)
		}
		if appDuration, found, _ := unstructured.NestedInt64(statusObj, "applicationState", "duration"); found {
			duration = formatDuration(appDuration)
		}
	}

	fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\t%s\n",
		name,
		status,
		attempts,
		startTime,
		duration,
		formatAge(sparkApp.GetCreationTimestamp()),
	)
	return w.Flush()
}

// 添加辅助方法用于格式化时间
func formatSparkTime(timeStr string) string {
	if timeStr == "" {
		return "<none>"
	}
	// 解析ISO时间格式
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return timeStr
	}
	return t.Format("2006-01-02 15:04:05")
}

func formatDuration(durationMs int64) string {
	if durationMs <= 0 {
		return "<none>"
	}
	duration := time.Duration(durationMs) * time.Millisecond
	if duration < time.Minute {
		return fmt.Sprintf("%ds", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%dm", int(duration.Minutes()))
	} else {
		return fmt.Sprintf("%dh%dm", int(duration.Hours()), int(duration.Minutes())%60)
	}
}

// 添加通用unstructured输出方法
func (c *NimbusKCEClient) outputUnstructuredTable(obj interface{}) error {
	jsonData, err := json.MarshalIndent(obj, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化失败: %w", err)
	}
	fmt.Println(string(jsonData))
	return nil
}

// getCustomResource 获取指定的自定义资源
func (c *NimbusKCEClient) getCustomResource(ctx context.Context, group, version, resource, resourceName, namespace, output string) error {
	gvr := schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resource,
	}

	var resourceClient dynamic.ResourceInterface
	if namespace != "" && !c.isClusterScoped(c.resourceToKind(resource)) {
		resourceClient = c.dynamicClient.Resource(gvr).Namespace(namespace)
	} else {
		resourceClient = c.dynamicClient.Resource(gvr)
	}

	if resourceName != "" {
		// 获取特定资源
		obj, err := resourceClient.Get(ctx, resourceName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("获取 %s 失败: %w", resource, err)
		}
		return c.outputResource(obj, output)
	}

	// 获取所有资源
	objList, err := resourceClient.List(ctx, metav1.ListOptions{})
	if err != nil {
		return fmt.Errorf("获取 %s 列表失败: %w", resource, err)
	}
	return c.outputResource(objList, output)
}

// handleGenericCustomResource 通用处理未知的自定义资源
func (c *NimbusKCEClient) handleGenericCustomResource(ctx context.Context, resourceType, resourceName, namespace, output string) error {
	// 尝试从API服务器发现资源信息
	discoveryClient := c.clientset.Discovery()

	// 获取所有API资源
	_, apiResourceLists, err := discoveryClient.ServerGroupsAndResources()
	if err != nil {
		return fmt.Errorf("发现API资源失败: %w", err)
	}

	// 查找匹配的资源
	var foundResource *metav1.APIResource
	var foundGroup string
	var foundVersion string

	for _, apiResourceList := range apiResourceLists {
		// 解析GroupVersion
		gv, err := schema.ParseGroupVersion(apiResourceList.GroupVersion)
		if err != nil {
			continue
		}

		for _, apiResource := range apiResourceList.APIResources {
			// 检查资源名称或短名称是否匹配
			if c.matchesResourceName(resourceType, apiResource) {
				foundResource = &apiResource
				foundGroup = gv.Group
				foundVersion = gv.Version
				break
			}
		}
		if foundResource != nil {
			break
		}
	}

	if foundResource == nil {
		return fmt.Errorf("未找到资源类型: %s", resourceType)
	}

	// 使用找到的资源信息获取资源
	return c.getCustomResource(ctx, foundGroup, foundVersion, foundResource.Name, resourceName, namespace, output)
}

// matchesResourceName 检查资源名称是否匹配
func (c *NimbusKCEClient) matchesResourceName(input string, apiResource metav1.APIResource) bool {
	input = strings.ToLower(input)

	// 检查完整资源名
	if input == strings.ToLower(apiResource.Name) {
		return true
	}

	// 检查单数形式
	if input == strings.ToLower(apiResource.SingularName) {
		return true
	}

	// 检查Kind的小写形式
	if input == strings.ToLower(apiResource.Kind) {
		return true
	}

	// 检查短名称
	for _, shortName := range apiResource.ShortNames {
		if input == strings.ToLower(shortName) {
			return true
		}
	}

	return false
}

// resourceToKind 将资源名转换为Kind (简化版本)
func (c *NimbusKCEClient) resourceToKind(resource string) string {
	// 简化的资源到Kind的映射
	kindMap := map[string]string{
		"workflows":                  "Workflow",
		"workflowtemplates":          "WorkflowTemplate",
		"cronworkflows":              "CronWorkflow",
		"flinkdeployments":           "FlinkDeployment",
		"flinksessionjobs":           "FlinkSessionJob",
		"sparkapplications":          "SparkApplication",
		"scheduledsparkapplications": "ScheduledSparkApplication",
	}

	if kind, exists := kindMap[resource]; exists {
		return kind
	}

	// 通用转换：首字母大写，去掉复数s
	if strings.HasSuffix(resource, "s") {
		resource = resource[:len(resource)-1]
	}
	return strings.Title(resource)
}
