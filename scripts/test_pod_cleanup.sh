#!/bin/bash

# Pod清理和状态同步测试脚本

BASE_URL="http://localhost:8080/api/v1"

echo "=== Pod清理和状态同步测试 ==="

# 1. 启动Pod监控
echo "1. 启动Pod监控..."
curl -X POST "${BASE_URL}/workflows/monitor/start" \
  -H "Content-Type: application/json" \
  -d '{}'

echo -e "\n"

# 2. 手动同步所有工作流状态
echo "2. 手动同步所有工作流状态..."
curl -X POST "${BASE_URL}/workflows/sync-all" \
  -H "Content-Type: application/json" \
  -d '{}'

echo -e "\n"

# 3. 手动清理指定集群的Pod
echo "3. 手动清理指定集群的Pod..."
curl -X POST "${BASE_URL}/workflows/cleanup-pods?cluster_id=your-cluster-id" \
  -H "Content-Type: application/json" \
  -d '{}'

echo -e "\n"

# 4. 获取工作流列表
echo "4. 获取工作流列表..."
curl -X GET "${BASE_URL}/workflows" \
  -H "Content-Type: application/json"

echo -e "\n"

# 5. 停止Pod监控
echo "5. 停止Pod监控..."
curl -X POST "${BASE_URL}/workflows/monitor/stop" \
  -H "Content-Type: application/json" \
  -d '{}'

echo -e "\n"

echo "=== 测试完成 ==="
