#!/bin/bash

# API 测试脚本

set -e

BASE_URL="http://localhost:8080"
API_PREFIX="/api/v1"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

function print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    
    print_status "测试: $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$API_PREFIX$endpoint" \
            -H "Content-Type: application/json" \
            -d "$data")
    else
        response=$(curl -s -w "%{http_code}" -X $method "$BASE_URL$API_PREFIX$endpoint")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "✅ 成功 ($status_code)"
        echo "响应: $body" | jq . 2>/dev/null || echo "响应: $body"
    else
        print_error "❌ 失败 (期望: $expected_status, 实际: $status_code)"
        echo "响应: $body"
    fi
    
    echo "---"
}

function wait_for_service() {
    print_status "等待服务启动..."
    
    for i in {1..30}; do
        if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
            print_status "✅ 服务已启动"
            return 0
        fi
        echo -n "."
        sleep 1
    done
    
    print_error "❌ 服务启动超时"
    exit 1
}

echo "🧪 Nimbus API 测试脚本"
echo "========================"

# 等待服务启动
wait_for_service

# 测试健康检查
print_status "📋 健康检查测试"
test_endpoint "GET" "/health" "" "200"

# 测试集群 API
print_status "📋 集群 API 测试"

# 创建集群（这可能会失败，因为需要有效的集群 ID）
cluster_data='{
    "name": "test-cluster",
    "description": "测试集群",
    "cluster_id": "2a9964c5-d3b9-4b95-8b32-5e752fcbb24a",
    "account_id": "test-user",
    "services": ["spark"]
}'

print_warning "注意: 以下测试可能失败，因为需要有效的 KCE 集群 ID 和认证"

test_endpoint "POST" "/clusters" "$cluster_data" "201"

# 获取集群列表
test_endpoint "GET" "/clusters" "" "200"

# 测试应用 API
print_status "📋 应用 API 测试"

# 这些测试可能会失败，因为需要有效的集群和应用
test_endpoint "GET" "/applications" "" "200"

# 测试任务 API
print_status "📋 任务 API 测试"
test_endpoint "GET" "/jobs" "" "200"

# 测试工作流 API
print_status "📋 工作流 API 测试"
test_endpoint "GET" "/workflows" "" "200"

print_status "🎉 API 测试完成"
echo ""
print_warning "注意事项:"
echo "- 某些测试可能失败，这是正常的，因为需要有效的 KCE 集群 ID"
echo "- 在生产环境中，需要配置正确的认证和集群信息"
echo "- 建议使用真实的集群 ID 进行完整测试"