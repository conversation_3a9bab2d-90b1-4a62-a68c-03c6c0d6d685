#!/bin/bash

# 构建脚本 - 编译 Nimbus 项目

set -e

PROJECT_NAME="nimbus"
VERSION=${VERSION:-"v1.0.0"}
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 输出彩色文本
function print_green {
    echo -e "\033[0;32m$1\033[0m"
}

function print_red {
    echo -e "\033[0;31m$1\033[0m"
}

function print_yellow {
    echo -e "\033[0;33m$1\033[0m"
}

print_green "🔨 构建 $PROJECT_NAME $VERSION"
print_yellow "构建时间: $BUILD_TIME"
print_yellow "Git 提交: $GIT_COMMIT"

# 创建构建目录
mkdir -p bin

# 设置构建标志
LDFLAGS="-X 'github.com/kingsoft/nimbus/pkg/version.Version=$VERSION' \
         -X 'github.com/kingsoft/nimbus/pkg/version.BuildTime=$BUILD_TIME' \
         -X 'github.com/kingsoft/nimbus/pkg/version.GitCommit=$GIT_COMMIT'"

print_yellow "📦 构建主程序..."
go build -ldflags "$LDFLAGS" -o bin/nimbus cmd/nimbus/main.go

print_yellow "🔧 构建命令行工具..."
go build -ldflags "$LDFLAGS" -o bin/kce-ctl cmd/kce-ctl/main.go

print_yellow "🧪 运行测试..."
go test -v ./... || print_red "⚠️  测试失败，但继续构建"

print_green "✅ 构建完成!"
echo "可执行文件:"
echo "  - bin/nimbus (主服务)"
echo "  - bin/kce-ctl (命令行工具)"

# 设置可执行权限
chmod +x bin/*

echo ""
print_green "🚀 运行方式:"
echo "  启动服务: ./bin/nimbus"
echo "  使用命令行: ./bin/kce-ctl --help"

# 如果提供了 --docker 参数，构建 Docker 镜像
if [[ "$1" == "--docker" ]]; then
    print_yellow "🐳 构建 Docker 镜像..."
    docker build -t nimbus:$VERSION -f deploy/docker/Dockerfile .
    print_green "Docker 镜像构建完成: nimbus:$VERSION"
fi 