#!/bin/bash

# 运行测试的脚本

# 输出彩色文本
function print_green {
    echo -e "\033[0;32m$1\033[0m"
}

function print_red {
    echo -e "\033[0;31m$1\033[0m"
}

function print_yellow {
    echo -e "\033[0;33m$1\033[0m"
}

print_green "======== Nimbus 测试脚本 ========"

# 参数处理
INSTALL_OPERATOR=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --install-operator)
            INSTALL_OPERATOR=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            print_red "未知参数: $1"
            exit 1
            ;;
    esac
done

# 运行所有测试
if [ "$VERBOSE" = true ]; then
    TEST_ARGS="-v"
else
    TEST_ARGS=""
fi

if [ "$INSTALL_OPERATOR" = true ]; then
    print_yellow "运行带 Operator 安装的测试..."
    RUN_OPERATOR_INSTALL_TEST=true go test $TEST_ARGS ./test/...
else
    print_yellow "运行测试（不安装 Operator）..."
    go test $TEST_ARGS ./test/...
fi

# 检查测试结果
if [ $? -eq 0 ]; then
    print_green "所有测试通过！"
else
    print_red "测试失败！"
    exit 1
fi 